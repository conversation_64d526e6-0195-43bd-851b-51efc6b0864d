{"name": "vue-migration-tools", "version": "1.0.0", "description": "Vue 2 to Vue 3 migration tools", "main": "index.js", "bin": {"vue2to3-migrator": "./bin/cli.js"}, "scripts": {"migrate": "node index.js", "test": "node test.js"}, "dependencies": {"@vue/eslint-config-standard": "^8.0.1", "ai": "^3.4.0", "@ai-sdk/openai": "^0.0.66", "axios": "^1.7.7", "chalk": "^4.1.2", "commander": "^11.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.28.0", "fs-extra": "^11.2.0", "glob": "^10.4.5", "gogocode": "^1.0.55", "gogocode-plugin-element": "^1.0.19", "gogocode-plugin-vue": "^1.0.56", "ora": "^5.4.1", "semver": "^7.6.3"}}