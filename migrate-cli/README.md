我正在设计一个 Vue 2 到 Vue 3 的规模化迁移方案，我需要你根据我的 task 编写迁移脚本。相关信息：

- aup-admin-ui 是 Vue 2 迁移的代码库
- vue-elment-admin 是 aup-admin-ui  原来的模板应用
- vue3-element-plus-admin 是新的工程模板（你要工作在这个工程中）
- migrate-cli 是未来我要编写的迁移脚本，将使用 gogocode 进行代码迁移，因此需要对齐原来的工程。

我的目标技术栈是： TypeScript/Javascript + vuex + vue router 4 + element-plus


## ✅ 1. 使用 Gogocode 转换 `package.json` 中 Vue 相关依赖版本

**目标**：将 `vue`, `vue-template-compiler`, `vue-router`, `vuex` 等升级为 Vue 3 兼容版本。

**实现建议**：

* 使用 `gogocode` 修改 JSON 文件结构（也可直接用 JS 操作）
* 或使用 Node 脚本处理 `package.json`

**示例代码**（Node）：

```js
const fs = require('fs');
const pkg = require('./package.json');

pkg.dependencies.vue = '^3.4.0';
delete pkg.dependencies['vue-template-compiler'];
pkg.devDependencies['@vue/compiler-sfc'] = '^3.4.0';

fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2));
```

---

## ✅ 2. 检查每个依赖是否存在 Vue 3 的兼容版本（可选）

**目标**：提前排查不兼容插件，有没有可能结合 AI 检查哪些是 Vue 的组件，如果配置了 AI 的话。

**做法**：

* 使用 `npm view <pkg> peerDependencies` 查看是否支持 Vue 3
* 可结合 [`npm-check-updates`](https://www.npmjs.com/package/npm-check-updates)

**示例代码**：

```bash
npx npm-check-updates -u
npm install
```

或检查某依赖的最新版本信息：

```bash
npm view vue-router versions
```

---

## ✅ 3. 批量使用 Gogocode 将 `.vue` 和 `.js` 文件迁移到 Vue 3

**目标**：将 Vue 2 的语法（如 `this.$refs`, `this.$emit`, `options API`）迁移为 Composition API 等。

**做法**：

* 使用 [Gogocode AST 转换脚本](https://github.com/thx/gogocode)
* 可以用现成的 Vue 2 ➝ 3 preset，或自己编写转换器

```js
const elementTransform = require('gogocode-plugin-element')

const options = {
    outRootPath: path.dirname(targetDir, '.'),
}

const vueResult = vueTransform.transform({
    source,
    path: filePath,
    options
}, {
    gogocode: require('gogocode')
}, {})

```

---

## ✅ 4. 记录转换失败的文件

**目标**：识别失败文件，供后续 AI 修复


## ✅ 5. 使用 AI 库（如 ChatGPT API）修复失败文件

**目标**：自动修复 Gogocode 无法迁移的边缘文件

**推荐**：

* 使用 Deepssek 等自动调用修复失败代码
* Prompt 应包括 Vue 2 和目标 Vue 3 的背景提示

**示例（伪代码）**：

```js
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

const { text } = await generateText({
    model: openai("gpt-4o"),
    prompt: prompt,
    maxTokens: 4000,
    })

///     
```



---

## ✅ 6. 使用 ESLint 自动修复格式和语法

**目标**：统一格式，修复可能的语法问题


## ✅ 7. 构建项目并使用 AI 修复构建错误

**目标**：完成构建，进一步修复遗留问题（如 TypeError, undefined 等）

**做法**：

* 执行 `vite build` 或 `webpack build`
* 捕捉构建错误输出，结合 AI 自动修复

## ✨ Bonus：整合为自动化 CLI 工具

你可以封装上述流程为一个 CLI 脚本，例如使用 Node + Commander：

```bash
npx vue2to3-migrator --input ./src --ai --eslint --build
```
