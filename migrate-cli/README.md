# Vue 2 到 Vue 3 迁移工具

一个全自动化的 Vue 2 到 Vue 3 迁移工具，使用 Gogocode 进行代码转换，结合 AI 修复复杂问题。

## ✨ 功能特性

- 🔄 **自动升级依赖**: 将 Vue 2 相关依赖升级到 Vue 3 兼容版本
- 🔍 **兼容性检查**: 检查项目依赖的 Vue 3 兼容性
- 🤖 **智能代码迁移**: 使用 Gogocode 批量转换 Vue 文件和 JS 文件
- 📝 **失败记录**: 详细记录转换失败的文件和原因
- 🧠 **AI 修复**: 使用 AI 修复 Gogocode 无法处理的复杂情况
- 🔧 **ESLint 修复**: 自动修复代码格式和语法问题
- 🏗️ **构建修复**: 自动构建项目并修复构建错误

## 📦 安装

```bash
# 克隆项目
git clone <repository-url>
cd migrate-cli

# 安装依赖
npm install

# 全局安装（可选）
npm link
```

## 🚀 快速开始

### 完整迁移

```bash
# 迁移当前目录的项目
npx vue2to3-migrator migrate

# 迁移指定项目
npx vue2to3-migrator migrate /path/to/your/vue2-project

# 跳过某些步骤
npx vue2to3-migrator migrate --skip-ai --skip-build
```

### 分步执行

```bash
# 仅检查兼容性
npx vue2to3-migrator check

# 仅升级依赖
npx vue2to3-migrator upgrade-deps

# 仅迁移代码
npx vue2to3-migrator migrate-code

# 仅 ESLint 修复
npx vue2to3-migrator fix-eslint

# 仅构建修复
npx vue2to3-migrator build-fix
```

我的目标技术栈是： TypeScript/Javascript + vuex + vue router 4 + element-plus


## ✅ 1. 使用 Gogocode 转换 `package.json` 中 Vue 相关依赖版本

**目标**：将 `vue`, `vue-template-compiler`, `vue-router`, `vuex` 等升级为 Vue 3 兼容版本。

**实现建议**：

* 使用 `gogocode` 修改 JSON 文件结构（也可直接用 JS 操作）
* 或使用 Node 脚本处理 `package.json`

**示例代码**（Node）：

```js
const fs = require('fs');
const pkg = require('./package.json');

pkg.dependencies.vue = '^3.4.0';
delete pkg.dependencies['vue-template-compiler'];
pkg.devDependencies['@vue/compiler-sfc'] = '^3.4.0';

fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2));
```

---

## ✅ 2. 检查每个依赖是否存在 Vue 3 的兼容版本（可选）

**目标**：提前排查不兼容插件，有没有可能结合 AI 检查哪些是 Vue 的组件，如果配置了 AI 的话。

**做法**：

* 使用 `npm view <pkg> peerDependencies` 查看是否支持 Vue 3
* 可结合 [`npm-check-updates`](https://www.npmjs.com/package/npm-check-updates)

**示例代码**：

```bash
npx npm-check-updates -u
npm install
```

或检查某依赖的最新版本信息：

```bash
npm view vue-router versions
```

---

## ✅ 3. 批量使用 Gogocode 将 `.vue` 和 `.js` 文件迁移到 Vue 3

**目标**：将 Vue 2 的语法（如 `this.$refs`, `this.$emit`, `options API`）迁移为 Composition API 等。

**做法**：

* 使用 [Gogocode AST 转换脚本](https://github.com/thx/gogocode)
* 可以用现成的 Vue 2 ➝ 3 preset，或自己编写转换器

```js
const elementTransform = require('gogocode-plugin-element')

const options = {
    outRootPath: path.dirname(targetDir, '.'),
}

const vueResult = vueTransform.transform({
    source,
    path: filePath,
    options
}, {
    gogocode: require('gogocode')
}, {})

```

---

## ✅ 4. 记录转换失败的文件

**目标**：识别失败文件，供后续 AI 修复


## ✅ 5. 使用 AI 库（如 ChatGPT API）修复失败文件

**目标**：自动修复 Gogocode 无法迁移的边缘文件

**推荐**：

* 使用 Deepssek 等自动调用修复失败代码
* Prompt 应包括 Vue 2 和目标 Vue 3 的背景提示

**示例（伪代码）**：

```js
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

const { text } = await generateText({
    model: openai("gpt-4o"),
    prompt: prompt,
    maxTokens: 4000,
    })

///     
```



---

## ✅ 6. 使用 ESLint 自动修复格式和语法

**目标**：统一格式，修复可能的语法问题


## ✅ 7. 构建项目并使用 AI 修复构建错误

**目标**：完成构建，进一步修复遗留问题（如 TypeError, undefined 等）

**做法**：

* 执行 `vite build` 或 `webpack build`
* 捕捉构建错误输出，结合 AI 自动修复

## ✨ Bonus：整合为自动化 CLI 工具

你可以封装上述流程为一个 CLI 脚本，例如使用 Node + Commander：

```bash
npx vue2to3-migrator --input ./src --ai --eslint --build
```
