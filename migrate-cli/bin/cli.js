#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const Vue2to3Migrator = require('../index');

const program = new Command();

program
  .name('vue2to3-migrator')
  .description('Vue 2 到 Vue 3 自动化迁移工具')
  .version('1.0.0');

program
  .command('migrate')
  .description('执行 Vue 2 到 Vue 3 迁移')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--skip-dependency-check', '跳过依赖兼容性检查')
  .option('--skip-ai', '跳过 AI 修复步骤')
  .option('--skip-eslint', '跳过 ESLint 自动修复')
  .option('--skip-build', '跳过构建和构建错误修复')
  .option('--ai-key <key>', 'OpenAI API Key')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .action(async (projectPath, options) => {
    try {
      console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移工具\n'));
      
      // 验证项目路径
      const resolvedPath = path.resolve(projectPath);
      if (!await fs.pathExists(resolvedPath)) {
        console.error(chalk.red(`❌ 项目路径不存在: ${resolvedPath}`));
        process.exit(1);
      }

      // 确认操作
      if (!options.dryRun) {
        console.log(chalk.yellow('⚠️  此操作将修改您的项目文件，建议先备份项目。'));
        console.log(chalk.gray(`项目路径: ${resolvedPath}`));
        
        // 在生产环境中，这里应该添加用户确认提示
        // const { confirm } = require('inquirer');
        // const { proceed } = await confirm({ message: '是否继续？' });
        // if (!proceed) process.exit(0);
      }

      // 创建迁移器实例
      const migrator = new Vue2to3Migrator(resolvedPath, {
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: options.skipEslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun
      });

      // 执行迁移
      await migrator.migrate();
      
      console.log(chalk.green('\n✅ 迁移完成！'));
      console.log(chalk.gray('请检查迁移报告和日志文件。'));
      
    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

program
  .command('check')
  .description('检查项目的 Vue 3 兼容性')
  .argument('[project-path]', '项目路径', process.cwd())
  .action(async (projectPath) => {
    try {
      const resolvedPath = path.resolve(projectPath);
      const DependencyChecker = require('../src/dependencyChecker');
      
      console.log(chalk.blue('🔍 检查 Vue 3 兼容性...\n'));
      
      const checker = new DependencyChecker(resolvedPath);
      const result = await checker.checkCompatibility();
      
      if (result.incompatible.length === 0) {
        console.log(chalk.green('🎉 所有依赖都兼容 Vue 3！'));
      } else {
        console.log(chalk.yellow(`⚠️  发现 ${result.incompatible.length} 个不兼容的依赖`));
        console.log(chalk.gray('运行 migrate 命令开始迁移。'));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ 检查失败:'), error.message);
      process.exit(1);
    }
  });

program
  .command('upgrade-deps')
  .description('仅升级 package.json 依赖')
  .argument('[project-path]', '项目路径', process.cwd())
  .action(async (projectPath) => {
    try {
      const resolvedPath = path.resolve(projectPath);
      const PackageUpgrader = require('../src/packageUpgrader');
      
      console.log(chalk.blue('📦 升级 package.json 依赖...\n'));
      
      const upgrader = new PackageUpgrader(resolvedPath);
      await upgrader.upgrade();
      
      console.log(chalk.green('✅ 依赖升级完成！'));
      console.log(chalk.gray('请运行 npm install 安装新依赖。'));
      
    } catch (error) {
      console.error(chalk.red('❌ 升级失败:'), error.message);
      process.exit(1);
    }
  });

program
  .command('migrate-code')
  .description('仅迁移代码文件')
  .argument('[project-path]', '项目路径', process.cwd())
  .action(async (projectPath) => {
    try {
      const resolvedPath = path.resolve(projectPath);
      const CodeMigrator = require('../src/codeMigrator');
      
      console.log(chalk.blue('🔄 迁移代码文件...\n'));
      
      const migrator = new CodeMigrator(resolvedPath);
      await migrator.migrate();
      
      console.log(chalk.green('✅ 代码迁移完成！'));
      
    } catch (error) {
      console.error(chalk.red('❌ 迁移失败:'), error.message);
      process.exit(1);
    }
  });

program
  .command('fix-eslint')
  .description('运行 ESLint 自动修复')
  .argument('[project-path]', '项目路径', process.cwd())
  .action(async (projectPath) => {
    try {
      const resolvedPath = path.resolve(projectPath);
      const ESLintFixer = require('../src/eslintFixer');
      
      console.log(chalk.blue('🔧 ESLint 自动修复...\n'));
      
      const fixer = new ESLintFixer(resolvedPath);
      await fixer.fix();
      
      console.log(chalk.green('✅ ESLint 修复完成！'));
      
    } catch (error) {
      console.error(chalk.red('❌ 修复失败:'), error.message);
      process.exit(1);
    }
  });

program
  .command('build-fix')
  .description('构建项目并修复错误')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--ai-key <key>', 'OpenAI API Key')
  .action(async (projectPath, options) => {
    try {
      const resolvedPath = path.resolve(projectPath);
      const BuildFixer = require('../src/buildFixer');
      const AIRepairer = require('../src/aiRepairer');
      
      console.log(chalk.blue('🏗️  构建项目并修复错误...\n'));
      
      const aiRepairer = new AIRepairer({ apiKey: options.aiKey });
      const buildFixer = new BuildFixer(resolvedPath, {
        buildCommand: options.buildCommand,
        aiRepairer
      });
      
      const result = await buildFixer.buildAndFix();
      
      if (result.success) {
        console.log(chalk.green('🎉 项目构建成功！'));
      } else {
        console.log(chalk.yellow('⚠️  构建仍有问题，请检查错误日志。'));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ 构建修复失败:'), error.message);
      process.exit(1);
    }
  });

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  if (process.env.DEBUG) {
    console.error(promise);
  }
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
