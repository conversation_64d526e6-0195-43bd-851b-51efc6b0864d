const Vue2to3Migrator = require('./index');
const path = require('path');
const chalk = require('chalk');

/**
 * 测试迁移工具
 */
async function testMigration() {
  try {
    console.log(chalk.bold.blue('🧪 测试 Vue 2 到 Vue 3 迁移工具\n'));
    
    // 使用 aup-admin-ui 作为测试项目
    const testProjectPath = path.join(__dirname, '../aup-admin-ui');
    
    console.log(chalk.gray(`测试项目: ${testProjectPath}`));
    
    // 创建迁移器实例（测试模式）
    const migrator = new Vue2to3Migrator(testProjectPath, {
      skipDependencyCheck: false,
      skipAIRepair: true, // 跳过 AI 修复以避免 API 调用
      skipESLint: false,
      skipBuild: true, // 跳过构建以避免长时间等待
      dryRun: true // 预览模式，不实际修改文件
    });
    
    // 执行迁移
    await migrator.migrate();
    
    console.log(chalk.green('\n✅ 测试完成！'));
    
  } catch (error) {
    console.error(chalk.red('\n❌ 测试失败:'), error.message);
    if (process.env.DEBUG) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testMigration();
}

module.exports = testMigration;
