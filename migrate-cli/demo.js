#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Vue 2 到 Vue 3 迁移工具演示版本
 * 这是一个简化版本，展示核心迁移逻辑
 */

// 简单的颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  gray: (text) => `\x1b[90m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`
};

class SimpleMigrator {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
    this.stats = {
      filesProcessed: 0,
      filesModified: 0,
      errors: []
    };
  }

  async migrate() {
    console.log(colors.bold(colors.blue('\n🚀 Vue 2 到 Vue 3 迁移工具 (演示版本)\n')));
    console.log(colors.gray(`项目路径: ${this.projectPath}\n`));

    try {
      // 步骤 1: 升级 package.json
      await this.upgradePackageJson();
      
      // 步骤 2: 简单的代码转换演示
      await this.demonstrateCodeTransforms();
      
      // 步骤 3: 生成报告
      this.generateReport();
      
      console.log(colors.green('\n✅ 演示完成！'));
      
    } catch (error) {
      console.error(colors.red('\n❌ 演示失败:'), error.message);
    }
  }

  async upgradePackageJson() {
    console.log(colors.blue('📦 步骤 1: 升级 package.json 依赖...'));
    
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    
    if (!fs.existsSync(packageJsonPath)) {
      console.log(colors.yellow('⚠️  package.json 不存在，跳过此步骤'));
      return;
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const originalJson = JSON.stringify(packageJson, null, 2);
      
      // 依赖映射
      const dependencyMapping = {
        'vue': '^3.4.0',
        'vue-router': '^4.5.0',
        'vuex': '^4.1.0',
        'element-ui': null, // 删除
        'element-plus': '^2.9.0',
        'vue-template-compiler': null, // 删除
        '@vue/compiler-sfc': '^3.4.0'
      };

      let modified = false;

      // 处理 dependencies
      if (packageJson.dependencies) {
        for (const [dep, newVersion] of Object.entries(dependencyMapping)) {
          if (packageJson.dependencies[dep]) {
            if (newVersion === null) {
              console.log(colors.yellow(`  🗑️  删除: ${dep}`));
              delete packageJson.dependencies[dep];
              modified = true;
            } else {
              console.log(colors.green(`  ⬆️  升级: ${dep} → ${newVersion}`));
              packageJson.dependencies[dep] = newVersion;
              modified = true;
            }
          }
        }

        // 添加新依赖
        if (!packageJson.dependencies['element-plus'] && packageJson.dependencies['element-ui']) {
          packageJson.dependencies['element-plus'] = '^2.9.0';
          console.log(colors.blue(`  ➕ 添加: element-plus`));
          modified = true;
        }
      }

      // 处理 devDependencies
      if (packageJson.devDependencies) {
        if (packageJson.devDependencies['vue-template-compiler']) {
          delete packageJson.devDependencies['vue-template-compiler'];
          packageJson.devDependencies['@vue/compiler-sfc'] = '^3.4.0';
          console.log(colors.green(`  🔄 替换: vue-template-compiler → @vue/compiler-sfc`));
          modified = true;
        }
      }

      if (modified) {
        // 创建备份
        fs.writeFileSync(packageJsonPath + '.backup', originalJson);
        console.log(colors.gray(`  📋 已创建备份: package.json.backup`));
        
        // 写入新的 package.json
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        console.log(colors.green('  ✅ package.json 更新完成'));
        this.stats.filesModified++;
      } else {
        console.log(colors.gray('  ⏸️  没有需要更新的依赖'));
      }

    } catch (error) {
      console.log(colors.red(`  ❌ 处理失败: ${error.message}`));
      this.stats.errors.push(`package.json: ${error.message}`);
    }
  }

  async demonstrateCodeTransforms() {
    console.log(colors.blue('\n🔄 步骤 2: 代码转换演示...'));

    // 演示一些常见的转换规则
    const transformExamples = [
      {
        name: 'Vue.extend 转换',
        before: `export default Vue.extend({
  name: 'MyComponent',
  data() {
    return { count: 0 }
  }
})`,
        after: `export default defineComponent({
  name: 'MyComponent',
  data() {
    return { count: 0 }
  }
})`
      },
      {
        name: 'Element UI 导入转换',
        before: `import { Button, Input } from 'element-ui'`,
        after: `import { ElButton, ElInput } from 'element-plus'`
      },
      {
        name: 'Vue Router 转换',
        before: `import VueRouter from 'vue-router'
Vue.use(VueRouter)`,
        after: `import { createRouter, createWebHistory } from 'vue-router'`
      },
      {
        name: 'Vuex 转换',
        before: `import Vuex from 'vuex'
Vue.use(Vuex)
export default new Vuex.Store({})`,
        after: `import { createStore } from 'vuex'
export default createStore({})`
      }
    ];

    transformExamples.forEach((example, index) => {
      console.log(colors.yellow(`\n  ${index + 1}. ${example.name}:`));
      console.log(colors.gray('     转换前:'));
      console.log(colors.gray(`     ${example.before.split('\n').join('\n     ')}`));
      console.log(colors.gray('     转换后:'));
      console.log(colors.green(`     ${example.after.split('\n').join('\n     ')}`));
    });

    // 实际转换一些关键文件
    console.log(colors.blue('\n  🔍 扫描并转换关键文件...'));

    const srcPath = path.join(this.projectPath, 'src');
    if (fs.existsSync(srcPath)) {
      const files = this.getAllFiles(srcPath, ['.vue', '.js', '.ts']);
      console.log(colors.gray(`  找到 ${files.length} 个文件需要处理`));

      // 转换关键文件
      await this.transformKeyFiles();

      files.slice(0, 5).forEach(file => {
        const relativePath = path.relative(this.projectPath, file);
        console.log(colors.gray(`    📄 ${relativePath}`));
        this.stats.filesProcessed++;
      });

      if (files.length > 5) {
        console.log(colors.gray(`    ... 还有 ${files.length - 5} 个文件`));
        this.stats.filesProcessed += files.length - 5;
      }
    } else {
      console.log(colors.yellow('  ⚠️  src 目录不存在'));
    }
  }

  async transformKeyFiles() {
    const keyFiles = [
      'src/main.js',
      'src/router/index.js',
      'src/store/index.js'
    ];

    for (const filePath of keyFiles) {
      const fullPath = path.join(this.projectPath, filePath);
      if (fs.existsSync(fullPath)) {
        await this.transformSingleFile(fullPath);
      }
    }
  }

  async transformSingleFile(filePath) {
    try {
      const relativePath = path.relative(this.projectPath, filePath);
      console.log(colors.blue(`    🔧 转换: ${relativePath}`));

      const content = fs.readFileSync(filePath, 'utf8');
      let transformedContent = content;

      // 应用转换规则
      transformedContent = this.applyTransformRules(transformedContent, filePath);

      if (transformedContent !== content) {
        // 创建备份
        fs.writeFileSync(filePath + '.backup', content);

        // 写入转换后的内容
        fs.writeFileSync(filePath, transformedContent);

        console.log(colors.green(`      ✅ 已转换并备份`));
        this.stats.filesModified++;

        // 显示主要变化
        this.showTransformChanges(content, transformedContent, relativePath);
      } else {
        console.log(colors.gray(`      ⏸️  无需转换`));
      }
    } catch (error) {
      console.log(colors.red(`      ❌ 转换失败: ${error.message}`));
      this.stats.errors.push(`${filePath}: ${error.message}`);
    }
  }

  applyTransformRules(content, filePath) {
    let transformed = content;

    // Vue 2 到 Vue 3 的基本转换
    if (filePath.includes('main.js')) {
      // 转换 main.js
      transformed = this.transformMainJs(transformed);
    } else if (filePath.includes('router')) {
      // 转换路由文件
      transformed = this.transformRouter(transformed);
    } else if (filePath.includes('store')) {
      // 转换 Vuex store
      transformed = this.transformStore(transformed);
    }

    // 通用转换规则
    transformed = this.applyCommonTransforms(transformed);

    return transformed;
  }

  transformMainJs(content) {
    let transformed = content;

    // 替换 Vue 导入
    transformed = transformed.replace(
      /import Vue from ['"]vue['"]/g,
      "import { createApp } from 'vue'"
    );

    // 替换 Element UI 导入
    transformed = transformed.replace(
      /import Element from ['"]element-ui['"]/g,
      "import ElementPlus from 'element-plus'"
    );

    transformed = transformed.replace(
      /import.*from ['"]element-ui\/lib\/locale\/lang\/.*['"]/g,
      "// Element Plus 语言包已内置"
    );

    // 替换样式导入
    transformed = transformed.replace(
      /import ['"].*element-variables\.scss['"]/g,
      "import 'element-plus/dist/index.css'"
    );

    // 替换 Vue.use
    transformed = transformed.replace(
      /Vue\.use\(Element[^)]*\)/g,
      "// app.use(ElementPlus) // 在 createApp 后使用"
    );

    // 替换 Vue.filter
    transformed = transformed.replace(
      /Vue\.filter\([^)]+\)/g,
      "// 全局过滤器需要改为全局属性或组合式函数"
    );

    // 替换 new Vue
    transformed = transformed.replace(
      /new Vue\(\{[\s\S]*?\}\)/g,
      `const app = createApp(App)
app.use(router)
app.use(store)
// app.use(ElementPlus)
app.mount('#app')`
    );

    return transformed;
  }

  transformRouter(content) {
    let transformed = content;

    // 替换 Vue Router 导入
    transformed = transformed.replace(
      /import Vue from ['"]vue['"]/g,
      ""
    );

    transformed = transformed.replace(
      /import Router from ['"]vue-router['"]/g,
      "import { createRouter, createWebHistory } from 'vue-router'"
    );

    // 删除 Vue.use(Router)
    transformed = transformed.replace(
      /Vue\.use\(Router\)/g,
      ""
    );

    // 替换路由创建
    transformed = transformed.replace(
      /const createRouter = \(\) => new Router\(/g,
      "const createRouterInstance = () => createRouter({\n  history: createWebHistory(),"
    );

    transformed = transformed.replace(
      /new Router\(/g,
      "createRouter({\n  history: createWebHistory(),"
    );

    // 修复通配符路由
    transformed = transformed.replace(
      /\{ path: '\*'/g,
      "{ path: '/:pathMatch(.*)*'"
    );

    return transformed;
  }

  transformStore(content) {
    let transformed = content;

    // 替换 Vuex 导入
    transformed = transformed.replace(
      /import Vuex from ['"]vuex['"]/g,
      "import { createStore } from 'vuex'"
    );

    // 删除 Vue.use(Vuex)
    transformed = transformed.replace(
      /Vue\.use\(Vuex\)/g,
      ""
    );

    // 替换 store 创建
    transformed = transformed.replace(
      /new Vuex\.Store\(/g,
      "createStore("
    );

    return transformed;
  }

  applyCommonTransforms(content) {
    let transformed = content;

    // Element UI 组件转换
    transformed = transformed.replace(
      /from ['"]element-ui['"]/g,
      "from 'element-plus'"
    );

    // Vue.extend 转换
    transformed = transformed.replace(
      /Vue\.extend\(/g,
      "defineComponent("
    );

    // 添加 defineComponent 导入（如果需要）
    if (transformed.includes('defineComponent(') && !transformed.includes('defineComponent')) {
      transformed = "import { defineComponent } from 'vue'\n" + transformed;
    }

    return transformed;
  }

  showTransformChanges(original, transformed, filePath) {
    const originalLines = original.split('\n');
    const transformedLines = transformed.split('\n');

    console.log(colors.yellow(`      📝 主要变化 (${filePath}):`));

    // 显示前几个重要的变化
    let changeCount = 0;
    for (let i = 0; i < Math.min(originalLines.length, transformedLines.length) && changeCount < 3; i++) {
      if (originalLines[i] !== transformedLines[i]) {
        console.log(colors.red(`        - ${originalLines[i].trim()}`));
        console.log(colors.green(`        + ${transformedLines[i].trim()}`));
        changeCount++;
      }
    }

    if (changeCount === 0) {
      console.log(colors.gray(`        (主要是结构性调整)`));
    }
  }

  getAllFiles(dir, extensions) {
    const files = [];
    
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllFiles(fullPath, extensions));
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
    
    return files;
  }

  generateReport() {
    console.log(colors.blue('\n📊 步骤 3: 生成迁移报告...'));
    
    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      stats: this.stats,
      recommendations: [
        '运行 npm install 安装新依赖',
        '检查并更新 Vue Router 配置',
        '检查并更新 Vuex Store 配置',
        '更新 Element UI 组件为 Element Plus',
        '测试应用功能确保正常工作',
        '考虑使用 Composition API 重构组件'
      ]
    };

    const reportPath = path.join(this.projectPath, 'migration-report-demo.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(colors.green(`  ✅ 报告已生成: ${reportPath}`));
    
    // 打印统计信息
    console.log(colors.bold('\n📈 迁移统计:'));
    console.log(`  处理文件: ${this.stats.filesProcessed} 个`);
    console.log(`  修改文件: ${this.stats.filesModified} 个`);
    console.log(`  错误数量: ${this.stats.errors.length} 个`);
    
    if (this.stats.errors.length > 0) {
      console.log(colors.red('\n❌ 错误列表:'));
      this.stats.errors.forEach(error => {
        console.log(colors.red(`  - ${error}`));
      });
    }

    console.log(colors.bold('\n📋 下一步建议:'));
    report.recommendations.forEach((rec, index) => {
      console.log(colors.yellow(`  ${index + 1}. ${rec}`));
    });
  }
}

// CLI 接口
function main() {
  const args = process.argv.slice(2);
  const projectPath = args[0] || process.cwd();
  
  console.log(colors.bold('Vue 2 到 Vue 3 迁移工具 - 演示版本'));
  console.log(colors.gray('这是一个简化的演示版本，展示迁移工具的核心功能\n'));
  
  const migrator = new SimpleMigrator(projectPath);
  migrator.migrate().catch(error => {
    console.error(colors.red('演示失败:'), error.message);
    process.exit(1);
  });
}

if (require.main === module) {
  main();
}

module.exports = SimpleMigrator;
