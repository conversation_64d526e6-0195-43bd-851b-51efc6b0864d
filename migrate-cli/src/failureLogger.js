const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 失败文件记录器
 */
class FailureLogger {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.logDir = path.join(projectPath, 'migration-logs');
    this.failureLogPath = path.join(this.logDir, 'failed-files.json');
    this.detailLogPath = path.join(this.logDir, 'migration-details.log');
    this.failures = [];
  }

  /**
   * 初始化日志目录
   */
  async initialize() {
    await fs.ensureDir(this.logDir);
    
    // 清空之前的日志
    if (await fs.pathExists(this.failureLogPath)) {
      await fs.remove(this.failureLogPath);
    }
    
    // 创建详细日志文件
    const timestamp = new Date().toISOString();
    await fs.writeFile(this.detailLogPath, 
      `Vue 2 to Vue 3 Migration Log\n` +
      `Started at: ${timestamp}\n` +
      `Project: ${this.projectPath}\n` +
      `${'='.repeat(50)}\n\n`
    );
  }

  /**
   * 记录失败的文件
   */
  async logFailure(filePath, error, context = {}) {
    const failure = {
      file: path.relative(this.projectPath, filePath),
      absolutePath: filePath,
      error: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context: {
        fileType: path.extname(filePath),
        fileSize: await this.getFileSize(filePath),
        ...context
      }
    };

    this.failures.push(failure);
    
    // 写入详细日志
    await this.writeDetailLog(failure);
    
    console.log(chalk.red(`❌ 记录失败文件: ${failure.file}`));
  }

  /**
   * 记录成功的文件
   */
  async logSuccess(filePath, transformInfo = {}) {
    const success = {
      file: path.relative(this.projectPath, filePath),
      timestamp: new Date().toISOString(),
      transformInfo
    };

    await this.writeDetailLog(success, 'SUCCESS');
  }

  /**
   * 记录跳过的文件
   */
  async logSkipped(filePath, reason = '') {
    const skipped = {
      file: path.relative(this.projectPath, filePath),
      reason,
      timestamp: new Date().toISOString()
    };

    await this.writeDetailLog(skipped, 'SKIPPED');
  }

  /**
   * 写入详细日志
   */
  async writeDetailLog(entry, type = 'FAILURE') {
    const logEntry = `[${entry.timestamp}] ${type}: ${entry.file}\n`;
    
    if (type === 'FAILURE') {
      const errorDetails = `  Error: ${entry.error}\n` +
                          `  Context: ${JSON.stringify(entry.context, null, 2)}\n`;
      await fs.appendFile(this.detailLogPath, logEntry + errorDetails + '\n');
    } else if (type === 'SUCCESS') {
      const successDetails = entry.transformInfo ? 
        `  Transforms: ${JSON.stringify(entry.transformInfo, null, 2)}\n` : '';
      await fs.appendFile(this.detailLogPath, logEntry + successDetails + '\n');
    } else if (type === 'SKIPPED') {
      const skipDetails = entry.reason ? `  Reason: ${entry.reason}\n` : '';
      await fs.appendFile(this.detailLogPath, logEntry + skipDetails + '\n');
    }
  }

  /**
   * 保存失败文件列表
   */
  async saveFailures() {
    if (this.failures.length === 0) {
      console.log(chalk.green('🎉 没有失败的文件!'));
      return;
    }

    // 保存 JSON 格式的失败列表
    await fs.writeJson(this.failureLogPath, {
      summary: {
        totalFailures: this.failures.length,
        timestamp: new Date().toISOString(),
        projectPath: this.projectPath
      },
      failures: this.failures
    }, { spaces: 2 });

    // 生成失败文件的简要报告
    await this.generateFailureReport();

    console.log(chalk.yellow(`📝 失败文件已记录到: ${this.failureLogPath}`));
  }

  /**
   * 生成失败报告
   */
  async generateFailureReport() {
    const reportPath = path.join(this.logDir, 'failure-report.md');
    
    let report = `# Vue 2 to Vue 3 Migration Failure Report\n\n`;
    report += `**Generated:** ${new Date().toISOString()}\n`;
    report += `**Project:** ${this.projectPath}\n`;
    report += `**Total Failures:** ${this.failures.length}\n\n`;

    // 按错误类型分组
    const errorGroups = this.groupFailuresByError();
    
    report += `## Failure Summary by Error Type\n\n`;
    Object.entries(errorGroups).forEach(([errorType, files]) => {
      report += `### ${errorType} (${files.length} files)\n\n`;
      files.forEach(failure => {
        report += `- \`${failure.file}\`\n`;
      });
      report += '\n';
    });

    // 详细失败信息
    report += `## Detailed Failure Information\n\n`;
    this.failures.forEach((failure, index) => {
      report += `### ${index + 1}. ${failure.file}\n\n`;
      report += `**Error:** ${failure.error}\n\n`;
      report += `**File Type:** ${failure.context.fileType}\n`;
      report += `**File Size:** ${failure.context.fileSize} bytes\n`;
      report += `**Timestamp:** ${failure.timestamp}\n\n`;
      
      if (failure.stack) {
        report += `**Stack Trace:**\n\`\`\`\n${failure.stack}\n\`\`\`\n\n`;
      }
      
      report += '---\n\n';
    });

    await fs.writeFile(reportPath, report);
    console.log(chalk.blue(`📊 失败报告已生成: ${reportPath}`));
  }

  /**
   * 按错误类型分组失败
   */
  groupFailuresByError() {
    const groups = {};
    
    this.failures.forEach(failure => {
      const errorType = this.categorizeError(failure.error);
      if (!groups[errorType]) {
        groups[errorType] = [];
      }
      groups[errorType].push(failure);
    });

    return groups;
  }

  /**
   * 分类错误类型
   */
  categorizeError(errorMessage) {
    if (errorMessage.includes('Parse error') || errorMessage.includes('SyntaxError')) {
      return 'Syntax Errors';
    }
    if (errorMessage.includes('Transform') || errorMessage.includes('AST')) {
      return 'Transform Errors';
    }
    if (errorMessage.includes('Import') || errorMessage.includes('Module')) {
      return 'Import/Module Errors';
    }
    if (errorMessage.includes('Vue') || errorMessage.includes('Component')) {
      return 'Vue-specific Errors';
    }
    if (errorMessage.includes('Element') || errorMessage.includes('UI')) {
      return 'UI Library Errors';
    }
    return 'Other Errors';
  }

  /**
   * 获取文件大小
   */
  async getFileSize(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 加载之前的失败记录
   */
  async loadPreviousFailures() {
    try {
      if (await fs.pathExists(this.failureLogPath)) {
        const data = await fs.readJson(this.failureLogPath);
        return data.failures || [];
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️  无法加载之前的失败记录'));
    }
    return [];
  }

  /**
   * 获取失败文件列表
   */
  getFailures() {
    return this.failures;
  }

  /**
   * 获取失败文件路径列表
   */
  getFailedFilePaths() {
    return this.failures.map(f => f.absolutePath);
  }

  /**
   * 清除失败记录
   */
  clearFailures() {
    this.failures = [];
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalFailures: this.failures.length,
      errorTypes: Object.keys(this.groupFailuresByError()),
      logDir: this.logDir,
      failureLogPath: this.failureLogPath,
      detailLogPath: this.detailLogPath
    };
  }
}

module.exports = FailureLogger;
