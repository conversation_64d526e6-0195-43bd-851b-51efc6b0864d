const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const glob = require('glob');
const gogocode = require('gogocode');
const vueTransform = require('gogocode-plugin-vue');
const elementTransform = require('gogocode-plugin-element');

/**
 * Vue 代码迁移器
 */
class CodeMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      srcDir: 'src',
      outputDir: null, // null 表示原地修改
      backupDir: 'backup',
      includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
      excludePatterns: ['node_modules/**', 'dist/**', 'build/**'],
      ...options
    };
    
    this.stats = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0,
      failedFiles: []
    };
  }

  /**
   * 执行代码迁移
   */
  async migrate() {
    try {
      console.log(chalk.blue('🔄 开始 Vue 代码迁移...'));
      
      // 创建备份
      await this.createBackup();
      
      // 获取需要迁移的文件
      const files = await this.getFilesToMigrate();
      console.log(chalk.gray(`找到 ${files.length} 个文件需要迁移`));
      
      this.stats.total = files.length;
      
      // 迁移文件
      for (const filePath of files) {
        await this.migrateFile(filePath);
      }
      
      // 打印统计结果
      this.printMigrationStats();
      
      return this.stats;
    } catch (error) {
      console.error(chalk.red('❌ 代码迁移失败:'), error.message);
      throw error;
    }
  }

  /**
   * 创建备份
   */
  async createBackup() {
    const backupPath = path.join(this.projectPath, this.options.backupDir);
    const srcPath = path.join(this.projectPath, this.options.srcDir);
    
    if (await fs.pathExists(srcPath)) {
      await fs.ensureDir(backupPath);
      await fs.copy(srcPath, path.join(backupPath, this.options.srcDir));
      console.log(chalk.yellow(`📋 已创建代码备份: ${backupPath}`));
    }
  }

  /**
   * 获取需要迁移的文件列表
   */
  async getFilesToMigrate() {
    const srcPath = path.join(this.projectPath, this.options.srcDir);
    const files = [];
    
    for (const pattern of this.options.includePatterns) {
      const matchedFiles = glob.sync(pattern, {
        cwd: srcPath,
        ignore: this.options.excludePatterns,
        absolute: false
      });
      
      files.push(...matchedFiles.map(file => path.join(srcPath, file)));
    }
    
    // 去重
    return [...new Set(files)];
  }

  /**
   * 迁移单个文件
   */
  async migrateFile(filePath) {
    try {
      const relativePath = path.relative(this.projectPath, filePath);
      process.stdout.write(chalk.gray(`迁移: ${relativePath} ... `));
      
      // 读取文件内容
      const source = await fs.readFile(filePath, 'utf8');
      
      // 根据文件类型选择迁移策略
      let transformedCode;
      const ext = path.extname(filePath);
      
      if (ext === '.vue') {
        transformedCode = await this.migrateVueFile(source, filePath);
      } else if (ext === '.js' || ext === '.ts') {
        transformedCode = await this.migrateJsFile(source, filePath);
      } else {
        console.log(chalk.yellow('跳过'));
        this.stats.skipped++;
        return;
      }
      
      // 写入转换后的代码
      if (transformedCode && transformedCode !== source) {
        await fs.writeFile(filePath, transformedCode, 'utf8');
        console.log(chalk.green('✅'));
        this.stats.success++;
      } else {
        console.log(chalk.gray('无变化'));
        this.stats.skipped++;
      }
      
    } catch (error) {
      console.log(chalk.red('❌'));
      this.stats.failed++;
      this.stats.failedFiles.push({
        file: filePath,
        error: error.message
      });
    }
  }

  /**
   * 迁移 Vue 文件
   */
  async migrateVueFile(source, filePath) {
    try {
      // 使用 gogocode-plugin-vue 进行转换
      const result = vueTransform.transform({
        source,
        path: filePath,
        options: {
          outRootPath: path.dirname(filePath),
          vue3: true
        }
      }, {
        gogocode
      }, {});
      
      let transformedCode = result.code || source;
      
      // 应用 Element UI 到 Element Plus 的转换
      if (transformedCode.includes('element-ui') || transformedCode.includes('el-')) {
        const elementResult = elementTransform.transform({
          source: transformedCode,
          path: filePath,
          options: {
            outRootPath: path.dirname(filePath)
          }
        }, {
          gogocode
        }, {});
        
        transformedCode = elementResult.code || transformedCode;
      }
      
      // 应用自定义转换规则
      transformedCode = this.applyCustomVueTransforms(transformedCode);
      
      return transformedCode;
    } catch (error) {
      throw new Error(`Vue 文件转换失败: ${error.message}`);
    }
  }

  /**
   * 迁移 JS/TS 文件
   */
  async migrateJsFile(source, filePath) {
    try {
      // 使用 gogocode 进行 AST 转换
      const ast = gogocode(source);
      
      // 应用 JS 转换规则
      this.applyJsTransforms(ast);
      
      return ast.generate();
    } catch (error) {
      throw new Error(`JS 文件转换失败: ${error.message}`);
    }
  }

  /**
   * 应用自定义 Vue 转换规则
   */
  applyCustomVueTransforms(code) {
    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['"]/g,
      "import { $1 } from 'element-plus'"
    );
    
    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['"]/g,
      "import 'element-plus/dist/index.css'"
    );
    
    // 替换 Vue 2 的全局 API
    code = code.replace(/Vue\.extend\(/g, 'defineComponent(');
    code = code.replace(/Vue\.component\(/g, 'app.component(');
    code = code.replace(/Vue\.use\(/g, 'app.use(');
    
    // 替换 $refs 访问方式（需要更复杂的 AST 处理）
    // 这里只做简单的字符串替换示例
    
    return code;
  }

  /**
   * 应用 JS 转换规则
   */
  applyJsTransforms(ast) {
    // 转换 Vue.extend 为 defineComponent
    ast.replace('Vue.extend($_$)', 'defineComponent($_$)');
    
    // 转换 new Vue() 为 createApp()
    ast.replace('new Vue($_$)', 'createApp($_$)');
    
    // 转换 Vue 实例方法
    ast.replace('$_$.prototype.$_$ = $_$', 'app.config.globalProperties.$_$ = $_$');
    
    // 转换 Vue.filter
    ast.replace('Vue.filter($_$, $_$)', 'app.config.globalProperties.$filters.$_$ = $_$');
    
    // 转换导入语句
    ast.replace(
      'import Vue from "vue"',
      'import { createApp } from "vue"'
    );
    
    return ast;
  }

  /**
   * 打印迁移统计
   */
  printMigrationStats() {
    console.log('\n' + chalk.bold('📊 代码迁移统计:'));
    console.log(`总计: ${this.stats.total} 个文件`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.gray(`⏸️  跳过: ${this.stats.skipped} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));
    
    if (this.stats.failedFiles.length > 0) {
      console.log(chalk.red('\n失败的文件:'));
      this.stats.failedFiles.forEach(({ file, error }) => {
        const relativePath = path.relative(this.projectPath, file);
        console.log(`  ${relativePath}: ${error}`);
      });
    }
    
    const successRate = ((this.stats.success / this.stats.total) * 100).toFixed(1);
    console.log(chalk.bold(`\n成功率: ${successRate}%`));
  }

  /**
   * 获取失败的文件列表
   */
  getFailedFiles() {
    return this.stats.failedFiles;
  }
}

module.exports = CodeMigrator;
