const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const semver = require('semver');

/**
 * Vue 3 兼容性检查器
 */
class DependencyChecker {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.packageJsonPath = path.join(projectPath, 'package.json');
    this.incompatibleDeps = [];
    this.compatibleDeps = [];
    this.unknownDeps = [];
  }

  /**
   * 检查所有依赖的 Vue 3 兼容性
   */
  async checkCompatibility() {
    try {
      console.log(chalk.blue('🔍 开始检查依赖的 Vue 3 兼容性...'));
      
      const packageJson = await fs.readJson(this.packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const results = await this.checkDependencies(allDeps);
      this.printCompatibilityReport(results);
      
      return results;
    } catch (error) {
      console.error(chalk.red('❌ 依赖兼容性检查失败:'), error.message);
      throw error;
    }
  }

  /**
   * 检查依赖列表
   */
  async checkDependencies(dependencies) {
    const results = {
      compatible: [],
      incompatible: [],
      unknown: [],
      total: Object.keys(dependencies).length
    };

    console.log(chalk.gray(`正在检查 ${results.total} 个依赖...`));

    for (const [depName, version] of Object.entries(dependencies)) {
      try {
        const compatibility = await this.checkSingleDependency(depName, version);
        results[compatibility.status].push(compatibility);
        
        // 显示进度
        process.stdout.write('.');
      } catch (error) {
        results.unknown.push({
          name: depName,
          version,
          status: 'unknown',
          error: error.message
        });
        process.stdout.write('?');
      }
    }

    console.log('\n');
    return results;
  }

  /**
   * 检查单个依赖的兼容性
   */
  async checkSingleDependency(depName, version) {
    // 跳过一些明显的系统依赖
    if (this.isSystemDependency(depName)) {
      return {
        name: depName,
        version,
        status: 'compatible',
        reason: 'System dependency'
      };
    }

    try {
      // 获取包的信息
      const packageInfo = await this.getPackageInfo(depName);
      
      // 检查是否有 Vue 3 支持
      const vue3Support = this.checkVue3Support(packageInfo);
      
      if (vue3Support.isCompatible) {
        return {
          name: depName,
          version,
          status: 'compatible',
          latestVersion: packageInfo.latestVersion,
          vue3Version: vue3Support.recommendedVersion,
          reason: vue3Support.reason
        };
      } else {
        return {
          name: depName,
          version,
          status: 'incompatible',
          latestVersion: packageInfo.latestVersion,
          reason: vue3Support.reason,
          alternatives: vue3Support.alternatives
        };
      }
    } catch (error) {
      return {
        name: depName,
        version,
        status: 'unknown',
        error: error.message
      };
    }
  }

  /**
   * 获取包信息
   */
  async getPackageInfo(packageName) {
    try {
      const result = execSync(`npm view ${packageName} --json`, { 
        encoding: 'utf8',
        timeout: 10000 
      });
      
      const packageInfo = JSON.parse(result);
      
      return {
        name: packageName,
        latestVersion: packageInfo.version,
        peerDependencies: packageInfo.peerDependencies || {},
        keywords: packageInfo.keywords || [],
        description: packageInfo.description || ''
      };
    } catch (error) {
      throw new Error(`Failed to get package info for ${packageName}: ${error.message}`);
    }
  }

  /**
   * 检查 Vue 3 支持
   */
  checkVue3Support(packageInfo) {
    const { name, peerDependencies, keywords, latestVersion } = packageInfo;

    // 检查已知的不兼容包
    const knownIncompatible = this.getKnownIncompatiblePackages();
    if (knownIncompatible[name]) {
      return {
        isCompatible: false,
        reason: 'Known incompatible with Vue 3',
        alternatives: knownIncompatible[name].alternatives
      };
    }

    // 检查已知的兼容包
    const knownCompatible = this.getKnownCompatiblePackages();
    if (knownCompatible[name]) {
      return {
        isCompatible: true,
        reason: 'Known compatible with Vue 3',
        recommendedVersion: knownCompatible[name].version || latestVersion
      };
    }

    // 检查 peerDependencies 中的 Vue 版本
    if (peerDependencies.vue) {
      const vueRange = peerDependencies.vue;
      if (semver.intersects(vueRange, '>=3.0.0')) {
        return {
          isCompatible: true,
          reason: `Supports Vue 3 (peerDependencies: ${vueRange})`,
          recommendedVersion: latestVersion
        };
      } else if (semver.intersects(vueRange, '<3.0.0')) {
        return {
          isCompatible: false,
          reason: `Only supports Vue 2 (peerDependencies: ${vueRange})`
        };
      }
    }

    // 基于关键词判断
    const vueRelated = keywords.some(keyword => 
      keyword.includes('vue') || keyword.includes('element')
    );

    if (vueRelated) {
      return {
        isCompatible: false,
        reason: 'Vue-related package without Vue 3 support indication'
      };
    }

    // 默认认为是兼容的（非 Vue 相关包）
    return {
      isCompatible: true,
      reason: 'Non-Vue specific package, likely compatible',
      recommendedVersion: latestVersion
    };
  }

  /**
   * 获取已知不兼容的包列表
   */
  getKnownIncompatiblePackages() {
    return {
      'element-ui': {
        alternatives: ['element-plus']
      },
      'vue-template-compiler': {
        alternatives: ['@vue/compiler-sfc']
      },
      '@vue/composition-api': {
        alternatives: ['Built into Vue 3']
      }
    };
  }

  /**
   * 获取已知兼容的包列表
   */
  getKnownCompatiblePackages() {
    return {
      'vue': { version: '^3.4.0' },
      'vue-router': { version: '^4.5.0' },
      'vuex': { version: '^4.1.0' },
      'element-plus': { version: '^2.9.0' },
      '@vue/compiler-sfc': { version: '^3.4.0' },
      'axios': {},
      'lodash': {},
      'moment': {},
      'dayjs': {},
      'echarts': {}
    };
  }

  /**
   * 判断是否为系统依赖
   */
  isSystemDependency(depName) {
    const systemDeps = [
      'fs-extra', 'path', 'util', 'crypto', 'os', 'http', 'https',
      'chalk', 'commander', 'inquirer', 'ora', 'semver',
      'webpack', 'babel', 'eslint', 'prettier', 'jest'
    ];

    return systemDeps.some(sysDep => depName.includes(sysDep));
  }

  /**
   * 打印兼容性报告
   */
  printCompatibilityReport(results) {
    console.log('\n' + chalk.bold('📊 Vue 3 兼容性检查报告:'));
    console.log(`总计: ${results.total} 个依赖\n`);

    // 兼容的依赖
    if (results.compatible.length > 0) {
      console.log(chalk.green(`✅ 兼容 Vue 3 (${results.compatible.length}个):`));
      results.compatible.forEach(dep => {
        console.log(`  ${dep.name} - ${dep.reason}`);
      });
      console.log('');
    }

    // 不兼容的依赖
    if (results.incompatible.length > 0) {
      console.log(chalk.red(`❌ 不兼容 Vue 3 (${results.incompatible.length}个):`));
      results.incompatible.forEach(dep => {
        console.log(`  ${dep.name} - ${dep.reason}`);
        if (dep.alternatives) {
          console.log(`    建议替换为: ${dep.alternatives.join(', ')}`);
        }
      });
      console.log('');
    }

    // 未知状态的依赖
    if (results.unknown.length > 0) {
      console.log(chalk.yellow(`❓ 无法确定 (${results.unknown.length}个):`));
      results.unknown.forEach(dep => {
        console.log(`  ${dep.name} - 需要手动检查`);
      });
      console.log('');
    }

    // 总结
    const compatibilityRate = ((results.compatible.length / results.total) * 100).toFixed(1);
    console.log(chalk.bold(`兼容性: ${compatibilityRate}%`));
    
    if (results.incompatible.length > 0) {
      console.log(chalk.yellow('\n⚠️  建议在继续迁移前先解决不兼容的依赖问题。'));
    }
  }
}

module.exports = DependencyChecker;
