const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 构建错误修复器
 */
class BuildFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      buildCommand: 'npm run build',
      maxRetries: 3,
      aiRepairer: null, // AI 修复器实例
      ...options
    };
    
    this.stats = {
      buildAttempts: 0,
      buildSuccess: false,
      errorsFound: [],
      errorsFixed: 0,
      finalBuildSuccess: false
    };
  }

  /**
   * 执行构建并修复错误
   */
  async buildAndFix() {
    try {
      console.log(chalk.blue('🏗️  开始构建项目并修复错误...'));
      
      // 首次构建尝试
      let buildResult = await this.attemptBuild();
      this.stats.buildAttempts++;
      
      if (buildResult.success) {
        console.log(chalk.green('🎉 项目构建成功！'));
        this.stats.buildSuccess = true;
        this.stats.finalBuildSuccess = true;
        return { success: true, attempts: this.stats.buildAttempts };
      }

      // 分析构建错误
      const errors = this.parseErrors(buildResult.output);
      this.stats.errorsFound = errors;
      
      if (errors.length === 0) {
        console.log(chalk.yellow('⚠️  构建失败但无法解析错误信息'));
        return { success: false, reason: 'Unable to parse build errors' };
      }

      console.log(chalk.yellow(`发现 ${errors.length} 个构建错误，开始修复...`));

      // 尝试修复错误
      for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
        console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxRetries}...`));
        
        const fixResult = await this.fixErrors(errors);
        
        if (fixResult.fixed > 0) {
          console.log(chalk.green(`✅ 修复了 ${fixResult.fixed} 个错误`));
          this.stats.errorsFixed += fixResult.fixed;
          
          // 重新构建
          buildResult = await this.attemptBuild();
          this.stats.buildAttempts++;
          
          if (buildResult.success) {
            console.log(chalk.green('🎉 修复后构建成功！'));
            this.stats.finalBuildSuccess = true;
            break;
          } else {
            // 更新错误列表
            errors = this.parseErrors(buildResult.output);
            console.log(chalk.yellow(`仍有 ${errors.length} 个错误需要修复`));
          }
        } else {
          console.log(chalk.yellow('⚠️  本轮未能修复任何错误'));
        }
      }

      this.printBuildStats();
      
      return {
        success: this.stats.finalBuildSuccess,
        attempts: this.stats.buildAttempts,
        errorsFixed: this.stats.errorsFixed,
        remainingErrors: errors.length
      };
      
    } catch (error) {
      console.error(chalk.red('❌ 构建修复过程失败:'), error.message);
      throw error;
    }
  }

  /**
   * 尝试构建项目
   */
  async attemptBuild() {
    try {
      console.log(chalk.gray(`执行构建命令: ${this.options.buildCommand}`));
      
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      return {
        success: true,
        output: output
      };
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 解析构建错误
   */
  parseErrors(buildOutput) {
    const errors = [];
    const lines = buildOutput.split('\n');
    
    let currentError = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 检测错误开始
      const errorMatch = this.detectErrorStart(line);
      if (errorMatch) {
        if (currentError) {
          errors.push(currentError);
        }
        currentError = {
          type: errorMatch.type,
          file: errorMatch.file,
          line: errorMatch.line,
          column: errorMatch.column,
          message: errorMatch.message,
          fullMessage: [line],
          severity: errorMatch.severity || 'error'
        };
      } else if (currentError && this.isErrorContinuation(line)) {
        currentError.fullMessage.push(line);
        if (line.trim()) {
          currentError.message += ' ' + line.trim();
        }
      } else if (currentError) {
        errors.push(currentError);
        currentError = null;
      }
    }
    
    if (currentError) {
      errors.push(currentError);
    }
    
    return this.categorizeErrors(errors);
  }

  /**
   * 检测错误开始
   */
  detectErrorStart(line) {
    // TypeScript 错误
    const tsError = line.match(/(.+\.ts)\((\d+),(\d+)\):\s*(error|warning)\s*TS(\d+):\s*(.+)/);
    if (tsError) {
      return {
        type: 'typescript',
        file: tsError[1],
        line: parseInt(tsError[2]),
        column: parseInt(tsError[3]),
        severity: tsError[4],
        code: tsError[5],
        message: tsError[6]
      };
    }

    // Vue 编译错误
    const vueError = line.match(/(.+\.vue):(\d+):(\d+):\s*(.*)/);
    if (vueError) {
      return {
        type: 'vue',
        file: vueError[1],
        line: parseInt(vueError[2]),
        column: parseInt(vueError[3]),
        message: vueError[4]
      };
    }

    // Webpack/Vite 错误
    const webpackError = line.match(/ERROR in (.+)/);
    if (webpackError) {
      return {
        type: 'webpack',
        file: webpackError[1],
        message: line
      };
    }

    // ESLint 错误
    const eslintError = line.match(/(.+\.(?:js|vue|ts)):(\d+):(\d+):\s*(error|warning)\s*(.+)/);
    if (eslintError) {
      return {
        type: 'eslint',
        file: eslintError[1],
        line: parseInt(eslintError[2]),
        column: parseInt(eslintError[3]),
        severity: eslintError[4],
        message: eslintError[5]
      };
    }

    // 通用错误
    const genericError = line.match(/Error:\s*(.+)/);
    if (genericError) {
      return {
        type: 'generic',
        message: genericError[1]
      };
    }

    return null;
  }

  /**
   * 检查是否为错误信息的继续
   */
  isErrorContinuation(line) {
    return line.startsWith('    ') || line.startsWith('\t') || 
           line.includes('at ') || line.includes('in ');
  }

  /**
   * 分类错误
   */
  categorizeErrors(errors) {
    return errors.map(error => {
      // 根据错误信息分类
      if (error.message.includes('Cannot find module')) {
        error.category = 'missing-module';
      } else if (error.message.includes('Property') && error.message.includes('does not exist')) {
        error.category = 'property-not-exist';
      } else if (error.message.includes('is not a function')) {
        error.category = 'not-a-function';
      } else if (error.message.includes('Vue 2') || error.message.includes('Vue3')) {
        error.category = 'vue-version';
      } else if (error.message.includes('element-ui') || error.message.includes('element-plus')) {
        error.category = 'ui-library';
      } else {
        error.category = 'other';
      }
      
      return error;
    });
  }

  /**
   * 修复错误
   */
  async fixErrors(errors) {
    let fixedCount = 0;
    
    for (const error of errors) {
      try {
        const fixed = await this.fixSingleError(error);
        if (fixed) {
          fixedCount++;
        }
      } catch (fixError) {
        console.log(chalk.yellow(`⚠️  修复错误失败: ${error.file} - ${fixError.message}`));
      }
    }
    
    return { fixed: fixedCount };
  }

  /**
   * 修复单个错误
   */
  async fixSingleError(error) {
    console.log(chalk.gray(`🔧 修复: ${error.file || 'unknown'} - ${error.category}`));
    
    switch (error.category) {
      case 'missing-module':
        return await this.fixMissingModule(error);
      case 'property-not-exist':
        return await this.fixPropertyNotExist(error);
      case 'vue-version':
        return await this.fixVueVersionIssue(error);
      case 'ui-library':
        return await this.fixUILibraryIssue(error);
      default:
        return await this.fixWithAI(error);
    }
  }

  /**
   * 修复缺失模块错误
   */
  async fixMissingModule(error) {
    const moduleMatch = error.message.match(/Cannot find module ['"]([^'"]+)['"]/);
    if (!moduleMatch) return false;
    
    const moduleName = moduleMatch[1];
    
    // 常见的模块映射
    const moduleMapping = {
      'element-ui': 'element-plus',
      'vue-template-compiler': '@vue/compiler-sfc',
      '@vue/composition-api': null // Vue 3 内置
    };
    
    if (moduleMapping.hasOwnProperty(moduleName)) {
      const replacement = moduleMapping[moduleName];
      if (replacement) {
        return await this.replaceImport(error.file, moduleName, replacement);
      } else {
        return await this.removeImport(error.file, moduleName);
      }
    }
    
    return false;
  }

  /**
   * 修复属性不存在错误
   */
  async fixPropertyNotExist(error) {
    // 使用 AI 修复复杂的属性错误
    return await this.fixWithAI(error);
  }

  /**
   * 修复 Vue 版本问题
   */
  async fixVueVersionIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 修复 UI 库问题
   */
  async fixUILibraryIssue(error) {
    return await this.fixWithAI(error);
  }

  /**
   * 使用 AI 修复错误
   */
  async fixWithAI(error) {
    if (!this.options.aiRepairer || !this.options.aiRepairer.isEnabled()) {
      return false;
    }

    try {
      const result = await this.options.aiRepairer.repairSingleFile({
        file: error.file,
        absolutePath: path.join(this.projectPath, error.file),
        error: error.message
      }, this.projectPath);
      
      return result.success;
    } catch (aiError) {
      console.log(chalk.yellow(`⚠️  AI 修复失败: ${aiError.message}`));
      return false;
    }
  }

  /**
   * 替换导入语句
   */
  async replaceImport(filePath, oldModule, newModule) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;
      
      const content = await fs.readFile(fullPath, 'utf8');
      const newContent = content.replace(
        new RegExp(`(['"])${oldModule}\\1`, 'g'),
        `$1${newModule}$1`
      );
      
      if (newContent !== content) {
        await fs.writeFile(fullPath, newContent, 'utf8');
        console.log(chalk.green(`✅ 替换导入: ${oldModule} → ${newModule}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 替换导入失败: ${error.message}`));
    }
    
    return false;
  }

  /**
   * 移除导入语句
   */
  async removeImport(filePath, moduleName) {
    try {
      const fullPath = path.join(this.projectPath, filePath);
      if (!await fs.pathExists(fullPath)) return false;
      
      const content = await fs.readFile(fullPath, 'utf8');
      const lines = content.split('\n');
      const newLines = lines.filter(line => 
        !line.includes(`'${moduleName}'`) && !line.includes(`"${moduleName}"`)
      );
      
      if (newLines.length !== lines.length) {
        await fs.writeFile(fullPath, newLines.join('\n'), 'utf8');
        console.log(chalk.green(`✅ 移除导入: ${moduleName}`));
        return true;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 移除导入失败: ${error.message}`));
    }
    
    return false;
  }

  /**
   * 打印构建统计
   */
  printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.stats.buildAttempts} 次`);
    console.log(`发现错误: ${this.stats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.stats.errorsFixed} 个`);
    console.log(`最终状态: ${this.stats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = BuildFixer;
