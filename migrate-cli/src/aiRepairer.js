const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { generateText } = require('ai');
const { openai } = require('@ai-sdk/openai');

/**
 * AI 代码修复器
 */
class AIRepairer {
  constructor(options = {}) {
    this.options = {
      apiKey: process.env.OPENAI_API_KEY || options.apiKey,
      model: options.model || 'gpt-4o',
      maxTokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.1,
      maxRetries: options.maxRetries || 3,
      ...options
    };

    if (!this.options.apiKey) {
      console.warn(chalk.yellow('⚠️  未设置 OPENAI_API_KEY，AI 修复功能将被禁用'));
      this.enabled = false;
    } else {
      this.enabled = true;
    }

    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 修复失败的文件列表
   */
  async repairFailedFiles(failedFiles, projectPath) {
    if (!this.enabled) {
      console.log(chalk.yellow('⚠️  AI 修复功能未启用，跳过修复步骤'));
      return { success: false, reason: 'AI repair disabled' };
    }

    console.log(chalk.blue(`🤖 开始使用 AI 修复 ${failedFiles.length} 个失败文件...`));

    const results = [];

    for (const failedFile of failedFiles) {
      try {
        const result = await this.repairSingleFile(failedFile, projectPath);
        results.push(result);
        
        if (result.success) {
          this.stats.success++;
        } else {
          this.stats.failed++;
        }
      } catch (error) {
        console.error(chalk.red(`❌ 修复文件失败: ${failedFile.file}`), error.message);
        this.stats.failed++;
        results.push({
          file: failedFile.file,
          success: false,
          error: error.message
        });
      }
      
      this.stats.attempted++;
    }

    this.printRepairStats();
    return results;
  }

  /**
   * 修复单个文件
   */
  async repairSingleFile(failedFile, projectPath) {
    const filePath = path.isAbsolute(failedFile.absolutePath) 
      ? failedFile.absolutePath 
      : path.join(projectPath, failedFile.file);

    console.log(chalk.gray(`🔧 修复: ${failedFile.file}...`));

    try {
      // 读取原始文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');
      
      // 生成修复提示
      const prompt = this.generateRepairPrompt(originalContent, failedFile);
      
      // 调用 AI 进行修复
      const repairedContent = await this.callAI(prompt);
      
      // 验证修复结果
      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // 备份原文件
        await this.backupFile(filePath);
        
        // 写入修复后的内容
        await fs.writeFile(filePath, repairedContent, 'utf8');
        
        console.log(chalk.green(`✅ 修复成功: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  修复结果验证失败: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (error) {
      console.log(chalk.red(`❌ 修复失败: ${failedFile.file}`));
      return {
        file: failedFile.file,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成修复提示
   */
  generateRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    let prompt = `你是一个专业的 Vue.js 开发者，需要帮助将 Vue 2 代码迁移到 Vue 3。

**任务**: 修复以下 ${fileExtension} 文件，使其兼容 Vue 3

**原始错误**: ${errorMessage}

**文件类型**: ${fileExtension}

**迁移要求**:
1. 将 Vue 2 语法转换为 Vue 3 兼容语法
2. 使用 Composition API（如果适用）
3. 更新 Element UI 为 Element Plus
4. 修复导入语句
5. 确保代码语法正确

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**请提供修复后的完整代码**:`;

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定要求**:
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 在 <script> 中使用 defineComponent 或 Composition API
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript 文件特定要求**:
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句`;
    }

    return prompt;
  }

  /**
   * 调用 AI API
   */
  async callAI(prompt) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.options.maxRetries; attempt++) {
      try {
        const { text } = await generateText({
          model: openai(this.options.model),
          prompt: prompt,
          maxTokens: this.options.maxTokens,
          temperature: this.options.temperature,
        });

        // 提取代码块
        const codeMatch = text.match(/```[\w]*\n([\s\S]*?)\n```/);
        if (codeMatch) {
          return codeMatch[1];
        } else {
          // 如果没有代码块，返回整个响应
          return text.trim();
        }
      } catch (error) {
        lastError = error;
        console.log(chalk.yellow(`⚠️  AI 调用失败 (尝试 ${attempt}/${this.options.maxRetries}): ${error.message}`));
        
        if (attempt < this.options.maxRetries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    throw new Error(`AI 调用失败，已重试 ${this.options.maxRetries} 次: ${lastError.message}`);
  }

  /**
   * 验证修复后的内容
   */
  validateRepairedContent(repairedContent, originalContent) {
    // 基本验证
    if (!repairedContent || repairedContent.trim().length === 0) {
      return false;
    }

    // 检查是否有明显的错误标记
    const errorMarkers = ['ERROR', 'FIXME', 'TODO: Fix', '// BROKEN'];
    if (errorMarkers.some(marker => repairedContent.includes(marker))) {
      return false;
    }

    // 检查代码长度是否合理（不应该太短或太长）
    const lengthRatio = repairedContent.length / originalContent.length;
    if (lengthRatio < 0.3 || lengthRatio > 3) {
      return false;
    }

    // 检查是否包含基本的代码结构
    if (originalContent.includes('export default') && !repairedContent.includes('export')) {
      return false;
    }

    return true;
  }

  /**
   * 备份文件
   */
  async backupFile(filePath) {
    const backupPath = `${filePath}.ai-backup`;
    await fs.copy(filePath, backupPath);
  }

  /**
   * 打印修复统计
   */
  printRepairStats() {
    console.log('\n' + chalk.bold('🤖 AI 修复统计:'));
    console.log(`尝试修复: ${this.stats.attempted} 个文件`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));
    
    if (this.stats.attempted > 0) {
      const successRate = ((this.stats.success / this.stats.attempted) * 100).toFixed(1);
      console.log(chalk.bold(`成功率: ${successRate}%`));
    }
  }

  /**
   * 检查 AI 功能是否可用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = AIRepairer;
