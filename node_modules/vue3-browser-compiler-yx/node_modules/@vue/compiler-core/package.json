{"name": "@vue/compiler-core", "version": "3.0.11", "description": "@vue/compiler-core", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "types": "dist/compiler-core.d.ts", "files": ["index.js", "dist"], "buildOptions": {"name": "VueCompilerCore", "formats": ["esm-bundler", "cjs"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-next.git", "directory": "packages/compiler-core"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-next/issues"}, "homepage": "https://github.com/vuejs/vue-next/tree/master/packages/compiler-core#readme", "dependencies": {"@vue/shared": "3.0.11", "@babel/parser": "^7.12.0", "@babel/types": "^7.12.0", "estree-walker": "^2.0.1", "source-map": "^0.6.1"}}