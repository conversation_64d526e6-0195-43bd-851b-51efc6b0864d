var VueCompilerDOM=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n=/;(?![^(]*\))/g,o=/:(.+)/;const r=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),s=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),c=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),i={},l=()=>{},p=()=>!1,a=/^on[^a-z]/,u=Object.assign,f=Array.isArray,d=e=>"string"==typeof e,h=e=>"symbol"==typeof e,m=e=>null!==e&&"object"==typeof e,g=t(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),y=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},v=/-(\w)/g,S=y((e=>e.replace(v,((e,t)=>t?t.toUpperCase():"")))),x=/\B([A-Z])/g,b=y((e=>e.replace(x,"-$1").toLowerCase())),T=y((e=>e.charAt(0).toUpperCase()+e.slice(1))),N=y((e=>e?`on${T(e)}`:""));function E(e){throw e}function k(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const _=Symbol(""),O=Symbol(""),C=Symbol(""),I=Symbol(""),$=Symbol(""),M=Symbol(""),w=Symbol(""),P=Symbol(""),V=Symbol(""),R=Symbol(""),L=Symbol(""),A=Symbol(""),B=Symbol(""),D=Symbol(""),F=Symbol(""),H=Symbol(""),j=Symbol(""),K=Symbol(""),U=Symbol(""),W=Symbol(""),G=Symbol(""),z=Symbol(""),J=Symbol(""),q=Symbol(""),Y=Symbol(""),Z=Symbol(""),X=Symbol(""),Q=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe={[_]:"Fragment",[O]:"Teleport",[C]:"Suspense",[I]:"KeepAlive",[$]:"BaseTransition",[M]:"openBlock",[w]:"createBlock",[P]:"createVNode",[V]:"createCommentVNode",[R]:"createTextVNode",[L]:"createStaticVNode",[A]:"resolveComponent",[B]:"resolveDynamicComponent",[D]:"resolveDirective",[F]:"withDirectives",[H]:"renderList",[j]:"renderSlot",[K]:"createSlots",[U]:"toDisplayString",[W]:"mergeProps",[G]:"toHandlers",[z]:"camelize",[J]:"capitalize",[q]:"toHandlerKey",[Y]:"setBlockTracking",[Z]:"pushScopeId",[X]:"popScopeId",[Q]:"withScopeId",[ee]:"withCtx",[te]:"unref",[ne]:"isRef"};function re(e){Object.getOwnPropertySymbols(e).forEach((t=>{oe[t]=e[t]}))}const se={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ce(e,t=se){return{type:0,children:e,helpers:[],components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function ie(e,t,n,o,r,s,c,i=!1,l=!1,p=se){return e&&(i?(e.helper(M),e.helper(w)):e.helper(P),c&&e.helper(F)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:c,isBlock:i,disableTracking:l,loc:p}}function le(e,t=se){return{type:17,loc:t,elements:e}}function pe(e,t=se){return{type:15,loc:t,properties:e}}function ae(e,t){return{type:16,loc:se,key:d(e)?ue(e,!0):e,value:t}}function ue(e,t,n=se,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function fe(e,t=se){return{type:8,loc:t,children:e}}function de(e,t=[],n=se){return{type:14,loc:n,callee:e,arguments:t}}function he(e,t,n=!1,o=!1,r=se){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function me(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:se}}function ge(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:se}}const ye=e=>4===e.type&&e.isStatic,ve=(e,t)=>e===t||e===b(t);function Se(e){return ve(e,"Teleport")?O:ve(e,"Suspense")?C:ve(e,"KeepAlive")?I:ve(e,"BaseTransition")?$:void 0}const xe=/^\d|[^\$\w]/,be=e=>!xe.test(e),Te=/^[A-Za-z_$\xA0-\uFFFF][\w$\xA0-\uFFFF]*(?:\s*\.\s*[A-Za-z_$\xA0-\uFFFF][\w$\xA0-\uFFFF]*|\[[^\]]+\])*$/,Ne=e=>!!e&&Te.test(e.trim());function Ee(e,t,n){const o={source:e.source.substr(t,n),start:ke(e.start,e.source,t),end:e.end};return null!=n&&(o.end=ke(e.start,e.source,t+n)),o}function ke(e,t,n=t.length){return _e(u({},e),t,n)}function _e(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Oe(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(d(t)?r.name===t:t.test(r.name)))return r}}function Ce(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Ie(s.arg,t))return s}}function Ie(e,t){return!(!e||!ye(e)||e.content!==t)}function $e(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function Me(e){return 5===e.type||2===e.type}function we(e){return 7===e.type&&"slot"===e.name}function Pe(e){return 1===e.type&&3===e.tagType}function Ve(e){return 1===e.type&&2===e.tagType}function Re(e,t,n){let o;const r=13===e.type?e.props:e.arguments[2];if(null==r||d(r))o=pe([t]);else if(14===r.type){const e=r.arguments[0];d(e)||15!==e.type?r.callee===G?o=de(n.helper(W),[pe([t]),r]):r.arguments.unshift(pe([t])):e.properties.unshift(t),!o&&(o=r)}else if(15===r.type){let e=!1;if(4===t.key.type){const n=t.key.content;e=r.properties.some((e=>4===e.key.type&&e.key.content===n))}e||r.properties.unshift(t),o=r}else o=de(n.helper(W),[pe([t]),r]);13===e.type?e.props=o:e.arguments[2]=o}function Le(e,t){return`_${t}_${e.replace(/[^\w]/g,"_")}`}const Ae=/&(gt|lt|amp|apos|quot);/g,Be={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},De={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:p,isPreTag:p,isCustomElement:p,decodeEntities:e=>e.replace(Ae,((e,t)=>Be[t])),onError:E,comments:!1};function Fe(e,t={}){const n=function(e,t){const n=u({},De);for(const o in t)n[o]=t[o]||De[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1}}(e,t),o=et(n);return ce(He(n,0,[]),tt(n,o))}function He(e,t,n){const o=nt(n),r=o?o.ns:0,s=[];for(;!it(e,t,n);){const c=e.source;let i;if(0===t||1===t)if(!e.inVPre&&ot(c,e.options.delimiters[0]))i=Ze(e,t);else if(0===t&&"<"===c[0])if(1===c.length);else if("!"===c[1])i=ot(c,"\x3c!--")?Ue(e):ot(c,"<!DOCTYPE")?We(e):ot(c,"<![CDATA[")&&0!==r?Ke(e,n):We(e);else if("/"===c[1])if(2===c.length);else{if(">"===c[2]){rt(e,3);continue}if(/[a-z]/i.test(c[2])){Je(e,1,o);continue}i=We(e)}else/[a-z]/i.test(c[1])?i=Ge(e,n):"?"===c[1]&&(i=We(e));if(i||(i=Xe(e,t)),f(i))for(let e=0;e<i.length;e++)je(s,i[e]);else je(s,i)}let c=!1;if(2!==t&&1!==t){for(let t=0;t<s.length;t++){const n=s[t];if(!e.inPre&&2===n.type)if(/[^\t\r\n\f ]/.test(n.content))n.content=n.content.replace(/[\t\r\n\f ]+/g," ");else{const e=s[t-1],o=s[t+1];!e||!o||3===e.type||3===o.type||1===e.type&&1===o.type&&/[\r\n]/.test(n.content)?(c=!0,s[t]=null):n.content=" "}3!==n.type||e.options.comments||(c=!0,s[t]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return c?s.filter(Boolean):s}function je(e,t){if(2===t.type){const n=nt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function Ke(e,t){rt(e,9);const n=He(e,3,t);return 0===e.source.length||rt(e,3),n}function Ue(e){const t=et(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)rt(e,s-r+1),r=s+1;rt(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),rt(e,e.source.length);return{type:3,content:n,loc:tt(e,t)}}function We(e){const t=et(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),rt(e,e.source.length)):(o=e.source.slice(n,r),rt(e,r+1)),{type:3,content:o,loc:tt(e,t)}}function Ge(e,t){const n=e.inPre,o=e.inVPre,r=nt(t),s=Je(e,0,r),c=e.inPre&&!n,i=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return s;t.push(s);const l=e.options.getTextMode(s,r),p=He(e,l,t);if(t.pop(),s.children=p,lt(e.source,s.tag))Je(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=p[0];e&&ot(e.loc.source,"\x3c!--")}return s.loc=tt(e,s.loc.start),c&&(e.inPre=!1),i&&(e.inVPre=!1),s}const ze=t("if,else,else-if,for,slot");function Je(e,t,n){const o=et(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],c=e.options.getNamespace(s,n);rt(e,r[0].length),st(e);const i=et(e),l=e.source;let p=qe(e,t);e.options.isPreTag(s)&&(e.inPre=!0),!e.inVPre&&p.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,u(e,i),e.source=l,p=qe(e,t).filter((e=>"v-pre"!==e.name)));let a=!1;0===e.source.length||(a=ot(e.source,"/>"),rt(e,a?2:1));let f=0;const d=e.options;if(!e.inVPre&&!d.isCustomElement(s)){const e=p.some((e=>7===e.type&&"is"===e.name));d.isNativeTag&&!e?d.isNativeTag(s)||(f=1):(e||Se(s)||d.isBuiltInComponent&&d.isBuiltInComponent(s)||/^[A-Z]/.test(s)||"component"===s)&&(f=1),"slot"===s?f=2:"template"===s&&p.some((e=>7===e.type&&ze(e.name)))&&(f=3)}return{type:1,ns:c,tag:s,tagType:f,props:p,isSelfClosing:a,children:[],loc:tt(e,o),codegenNode:void 0}}function qe(e,t){const n=[],o=new Set;for(;e.source.length>0&&!ot(e.source,">")&&!ot(e.source,"/>");){if(ot(e.source,"/")){rt(e,1),st(e);continue}const r=Ye(e,o);0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),st(e)}return n}function Ye(e,t){const n=et(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;rt(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(st(e),rt(e,1),st(e),r=function(e){const t=et(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){rt(e,1);const t=e.source.indexOf(o);-1===t?n=Qe(e,e.source.length,4):(n=Qe(e,t,4),rt(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=Qe(e,t[0].length,4)}return{content:n,isQuoted:r,loc:tt(e,t)}}(e));const s=tt(e,n);if(!e.inVPre&&/^(v-|:|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o),c=t[1]||(ot(o,":")?"bind":ot(o,"@")?"on":"slot");let i;if(t[2]){const r="slot"===c,s=o.lastIndexOf(t[2]),l=tt(e,ct(e,n,s),ct(e,n,s+t[2].length+(r&&t[3]||"").length));let p=t[2],a=!0;p.startsWith("[")?(a=!1,p.endsWith("]"),p=p.substr(1,p.length-2)):r&&(p+=t[3]||""),i={type:4,content:p,isStatic:a,constType:a?3:0,loc:l}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=ke(e.start,r.content),e.source=e.source.slice(1,-1)}return{type:7,name:c,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:t[3]?t[3].substr(1).split("."):[],loc:s}}return{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function Ze(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=et(e);rt(e,n.length);const c=et(e),i=et(e),l=r-n.length,p=e.source.slice(0,l),a=Qe(e,l,t),u=a.trim(),f=a.indexOf(u);f>0&&_e(c,p,f);return _e(i,p,l-(a.length-u.length-f)),rt(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:tt(e,c,i)},loc:tt(e,s)}}function Xe(e,t){const n=["<",e.options.delimiters[0]];3===t&&n.push("]]>");let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=et(e);return{type:2,content:Qe(e,o,t),loc:tt(e,r)}}function Qe(e,t,n){const o=e.source.slice(0,t);return rt(e,t),2===n||3===n||-1===o.indexOf("&")?o:e.options.decodeEntities(o,4===n)}function et(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function tt(e,t,n){return{start:t,end:n=n||et(e),source:e.originalSource.slice(t.offset,n.offset)}}function nt(e){return e[e.length-1]}function ot(e,t){return e.startsWith(t)}function rt(e,t){const{source:n}=e;_e(e,n,t),e.source=n.slice(t)}function st(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&rt(e,t[0].length)}function ct(e,t,n){return ke(t,e.originalSource.slice(t.offset,n),n)}function it(e,t,n){const o=e.source;switch(t){case 0:if(ot(o,"</"))for(let e=n.length-1;e>=0;--e)if(lt(o,n[e].tag))return!0;break;case 1:case 2:{const e=nt(n);if(e&&lt(o,e.tag))return!0;break}case 3:if(ot(o,"]]>"))return!0}return!o}function lt(e,t){return ot(e,"</")&&e.substr(2,t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function pt(e,t){ut(e,t,at(e,e.children[0]))}function at(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!Ve(t)}function ut(e,t,n=!1){let o=!1,r=!0;const{children:s}=e;for(let c=0;c<s.length;c++){const e=s[c];if(1===e.type&&0===e.tagType){const s=n?0:ft(e,t);if(s>0){if(s<3&&(r=!1),s>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),o=!0;continue}}else{const n=e.codegenNode;if(13===n.type){const o=mt(n);if((!o||512===o||1===o)&&dt(e,t)>=2){const o=ht(e);o&&(n.props=t.hoist(o))}}}}else if(12===e.type){const n=ft(e.content,t);n>0&&(n<3&&(r=!1),n>=2&&(e.codegenNode=t.hoist(e.codegenNode),o=!0))}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,ut(e,t),n&&t.scopes.vSlot--}else if(11===e.type)ut(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)ut(e.branches[n],t,1===e.branches[n].children.length)}r&&o&&t.transformHoist&&t.transformHoist(s,t,e)}function ft(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(mt(r))return n.set(e,0),0;{let o=3;const s=dt(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=ft(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=ft(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}return r.isBlock&&(t.removeHelper(M),t.removeHelper(w),r.isBlock=!1,t.helper(P)),n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:return 0;case 5:case 12:return ft(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(d(o)||h(o))continue;const r=ft(o,t);if(0===r)return 0;r<s&&(s=r)}return s;default:return 0}}function dt(e,t){let n=3;const o=ht(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],c=ft(r,t);if(0===c)return c;if(c<n&&(n=c),4!==s.type)return 0;const i=ft(s,t);if(0===i)return i;i<n&&(n=i)}}return n}function ht(e){const t=e.codegenNode;if(13===t.type)return t.props}function mt(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function gt(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:c={},transformHoist:p=null,isBuiltInComponent:a=l,isCustomElement:u=l,expressionPlugins:f=[],scopeId:d=null,slotted:h=!0,ssr:m=!1,ssrCssVars:g="",bindingMetadata:y=i,inline:v=!1,isTS:x=!1,onError:b=E}){const N=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),k={selfName:N&&T(S(N[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:c,transformHoist:p,isBuiltInComponent:a,isCustomElement:u,expressionPlugins:f,scopeId:d,slotted:h,ssr:m,ssrCssVars:g,bindingMetadata:y,inline:v,isTS:x,onError:b,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,helper(e){const t=k.helpers.get(e)||0;return k.helpers.set(e,t+1),e},removeHelper(e){const t=k.helpers.get(e);if(t){const n=t-1;n?k.helpers.set(e,n):k.helpers.delete(e)}},helperString:e=>`_${oe[k.helper(e)]}`,replaceNode(e){k.parent.children[k.childIndex]=k.currentNode=e},removeNode(e){const t=e?k.parent.children.indexOf(e):k.currentNode?k.childIndex:-1;e&&e!==k.currentNode?k.childIndex>t&&(k.childIndex--,k.onNodeRemoved()):(k.currentNode=null,k.onNodeRemoved()),k.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){k.hoists.push(e);const t=ue(`_hoisted_${k.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>ge(++k.cached,e,t)};return k}function yt(e,t){const n=gt(e,t);vt(e,n),t.hoistStatic&&pt(e,n),t.ssr||function(e,t){const{helper:n,removeHelper:o}=t,{children:r}=e;if(1===r.length){const t=r[0];if(at(e,t)&&t.codegenNode){const r=t.codegenNode;13===r.type&&(r.isBlock||(o(P),r.isBlock=!0,n(M),n(w))),e.codegenNode=r}else e.codegenNode=t}else if(r.length>1){let o=64;e.codegenNode=ie(t,n(_),void 0,e.children,o+"",void 0,void 0,!0)}}(e,n),e.helpers=[...n.helpers.keys()],e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached}function vt(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(f(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(V);break;case 5:t.ssr||t.helper(U);break;case 9:for(let n=0;n<e.branches.length;n++)vt(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];d(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,vt(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function St(e,t){const n=d(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(we))return;const s=[];for(let c=0;c<r.length;c++){const i=r[c];if(7===i.type&&n(i.name)){r.splice(c,1),c--;const n=t(e,i,o);n&&s.push(n)}}return s}}}const xt="/*#__PURE__*/";function bt(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:c=!1,runtimeGlobalName:i="Vue",runtimeModuleName:l="vue",ssr:p=!1}){const a={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:c,runtimeGlobalName:i,runtimeModuleName:l,ssr:p,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${oe[e]}`,push(e,t){a.code+=e},indent(){u(++a.indentLevel)},deindent(e=!1){e?--a.indentLevel:u(--a.indentLevel)},newline(){u(a.indentLevel)}};function u(e){a.push("\n"+"  ".repeat(e))}return a}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:c,deindent:i,newline:l,ssr:p}=n,a=e.helpers.length>0,u=!s&&"module"!==o;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,c=e=>`${oe[e]}: _${oe[e]}`;if(e.helpers.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[P,V,R,L].filter((t=>e.helpers.includes(t))).map(c).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o(),e.forEach(((e,r)=>{e&&(n(`const _hoisted_${r+1} = `),kt(e,t),o())})),t.pure=!1})(e.hoists,t),o(),n("return ")}(e,n);if(r(`function ${p?"ssrRender":"render"}(${(p?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),c(),u&&(r("with (_ctx) {"),c(),a&&(r(`const { ${e.helpers.map((e=>`${oe[e]}: _${oe[e]}`)).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(Tt(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Tt(e.directives,"directive",n),e.temps>0&&l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),p||r("return "),e.codegenNode?kt(e.codegenNode,n):r("null"),u&&(i(),r("}")),i(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function Tt(e,t,{helper:n,push:o,newline:r}){const s=n("component"===t?A:D);for(let c=0;c<e.length;c++){let n=e[c];const i=n.endsWith("__self");i&&(n=n.slice(0,-6)),o(`const ${Le(n,t)} = ${s}(${JSON.stringify(n)}${i?", true":""})`),c<e.length-1&&r()}}function Nt(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),Et(e,t,n),n&&t.deindent(),t.push("]")}function Et(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let c=0;c<e.length;c++){const i=e[c];d(i)?r(i):f(i)?Nt(i,t):kt(i,t),c<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function kt(e,t){if(d(e))t.push(e);else if(h(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:kt(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:_t(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(xt);n(`${o(U)}(`),kt(e.content,t),n(")")}(e,t);break;case 12:kt(e.codegenNode,t);break;case 8:Ot(e,t);break;case 3:break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:c,children:i,patchFlag:l,dynamicProps:p,directives:a,isBlock:u,disableTracking:f}=e;a&&n(o(F)+"(");u&&n(`(${o(M)}(${f?"true":""}), `);r&&n(xt);n(o(u?w:P)+"(",e),Et(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,c,i,l,p]),t),n(")"),u&&n(")");a&&(n(", "),kt(a,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=d(e.callee)?e.callee:o(e.callee);r&&n(xt);n(s+"(",e),Et(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:c}=e;if(!c.length)return void n("{}",e);const i=c.length>1||!1;n(i?"{":"{ "),i&&o();for(let l=0;l<c.length;l++){const{key:e,value:o}=c[l];Ct(e,t),n(": "),kt(o,t),l<c.length-1&&(n(","),s())}i&&r(),n(i?"}":" }")}(e,t);break;case 17:!function(e,t){Nt(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:c,body:i,newline:l,isSlot:p}=e;p&&n(`_${oe[ee]}(`);n("(",e),f(s)?Et(s,t):s&&kt(s,t);n(") => "),(l||i)&&(n("{"),o());c?(l&&n("return "),f(c)?Nt(c,t):kt(c,t)):i&&kt(i,t);(l||i)&&(r(),n("}"));p&&n(")")}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:c,indent:i,deindent:l,newline:p}=t;if(4===n.type){const e=!be(n.content);e&&c("("),_t(n,t),e&&c(")")}else c("("),kt(n,t),c(")");s&&i(),t.indentLevel++,s||c(" "),c("? "),kt(o,t),t.indentLevel--,s&&p(),s||c(" "),c(": ");const a=19===r.type;a||t.indentLevel++;kt(r,t),a||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:c}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(Y)}(-1),`),c());n(`_cache[${e.index}] = `),kt(e.value,t),e.isVNode&&(n(","),c(),n(`${o(Y)}(1),`),c(),n(`_cache[${e.index}]`),s());n(")")}(e,t)}}function _t(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function Ot(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];d(o)?t.push(o):kt(o,t)}}function Ct(e,t){const{push:n}=t;if(8===e.type)n("["),Ot(e,t),n("]");else if(e.isStatic){n(be(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function It(e,t,n=!1,o=!1){return e}const $t=St(/^(if|else|else-if)$/,((e,t,n)=>Mt(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),c=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(c+=e.branches.length)}return()=>{if(o)e.codegenNode=Pt(t,c,n);else{(function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode)).alternate=Pt(t,c+e.branches.length-1,n)}}}))));function Mt(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=ue("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=wt(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const c=r[s];if(!c||2!==c.type||c.content.trim().length){if(c&&9===c.type){n.removeNode();const r=wt(e,t);c.branches.push(r);const s=o&&o(c,r,!1);vt(r,n),s&&s(),n.currentNode=null}break}n.removeNode(c)}}}function wt(e,t){return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:3!==e.tagType||Oe(e,"for")?[e]:e.children,userKey:Ce(e,"key")}}function Pt(e,t,n){return e.condition?me(e.condition,Vt(e,t,n),de(n.helper(V),['""',"true"])):Vt(e,t,n)}function Vt(e,t,n){const{helper:o,removeHelper:r}=n,s=ae("key",ue(`${t}`,!1,se,2)),{children:c}=e,i=c[0];if(1!==c.length||1!==i.type){if(1===c.length&&11===i.type){const e=i.codegenNode;return Re(e,s,n),e}{let t=64;return ie(n,o(_),pe([s]),c,t+"",void 0,void 0,!0,!1,e.loc)}}{const e=i.codegenNode;return 13!==e.type||e.isBlock||(r(P),e.isBlock=!0,o(M),o(w)),Re(e,s,n),e}}const Rt=St("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return Lt(e,t,n,(t=>{const s=de(o(H),[t.source]),c=Ce(e,"key"),i=c?ae("key",6===c.type?ue(c.value.content,!0):c.exp):null,l=4===t.source.type&&t.source.constType>0,p=l?64:c?128:256;return t.codegenNode=ie(n,o(_),void 0,s,p+"",void 0,void 0,!0,!l,e.loc),()=>{let c;const p=Pe(e),{children:a}=t,u=1!==a.length||1!==a[0].type,f=Ve(e)?e:p&&1===e.children.length&&Ve(e.children[0])?e.children[0]:null;f?(c=f.codegenNode,p&&i&&Re(c,i,n)):u?c=ie(n,o(_),i?pe([i]):void 0,e.children,"64",void 0,void 0,!0):(c=a[0].codegenNode,p&&i&&Re(c,i,n),c.isBlock!==!l&&(c.isBlock?(r(M),r(w)):r(P)),c.isBlock=!l,c.isBlock?(o(M),o(w)):o(P)),s.arguments.push(he(jt(t.parseResult),c,!0))}}))}));function Lt(e,t,n,o){if(!t.exp)return;const r=Ft(t.exp);if(!r)return;const{scopes:s}=n,{source:c,value:i,key:l,index:p}=r,a={type:11,loc:t.loc,source:c,valueAlias:i,keyAlias:l,objectIndexAlias:p,parseResult:r,children:Pe(e)?e.children:[e]};n.replaceNode(a),s.vFor++;const u=o&&o(a);return()=>{s.vFor--,u&&u()}}const At=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Bt=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Dt=/^\(|\)$/g;function Ft(e,t){const n=e.loc,o=e.content,r=o.match(At);if(!r)return;const[,s,c]=r,i={source:Ht(n,c.trim(),o.indexOf(c,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(Dt,"").trim();const p=s.indexOf(l),a=l.match(Bt);if(a){l=l.replace(Bt,"").trim();const e=a[1].trim();let t;if(e&&(t=o.indexOf(e,p+l.length),i.key=Ht(n,e,t)),a[2]){const r=a[2].trim();r&&(i.index=Ht(n,r,o.indexOf(r,i.key?t+e.length:p+l.length)))}}return l&&(i.value=Ht(n,l,p)),i}function Ht(e,t,n){return ue(t,!1,Ee(e,n,t.length))}function jt({value:e,key:t,index:n}){const o=[];return e&&o.push(e),t&&(e||o.push(ue("_",!1)),o.push(t)),n&&(t||(e||o.push(ue("_",!1)),o.push(ue("__",!1))),o.push(n)),o}const Kt=ue("undefined",!1),Ut=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Oe(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},Wt=(e,t,n)=>he(e,t,!1,!0,t.length?t[0].loc:n);function Gt(e,t,n=Wt){t.helper(ee);const{children:o,loc:r}=e,s=[],c=[],i=(e,t)=>ae("default",n(e,t,r));let l=t.scopes.vSlot>0||t.scopes.vFor>0;const p=Oe(e,"slot",!0);if(p){const{arg:e,exp:t}=p;e&&!ye(e)&&(l=!0),s.push(ae(e||ue("default",!0),n(t,o,r)))}let a=!1,u=!1;const f=[],d=new Set;for(let g=0;g<o.length;g++){const e=o[g];let r;if(!Pe(e)||!(r=Oe(e,"slot",!0))){3!==e.type&&f.push(e);continue}if(p)break;a=!0;const{children:i,loc:h}=e,{arg:m=ue("default",!0),exp:y}=r;let v;ye(m)?v=m?m.content:"default":l=!0;const S=n(y,i,h);let x,b,T;if(x=Oe(e,"if"))l=!0,c.push(me(x.exp,zt(m,S),Kt));else if(b=Oe(e,/^else(-if)?$/,!0)){let e,t=g;for(;t--&&(e=o[t],3===e.type););if(e&&Pe(e)&&Oe(e,"if")){o.splice(g,1),g--;let e=c[c.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=b.exp?me(b.exp,zt(m,S),Kt):zt(m,S)}}else if(T=Oe(e,"for")){l=!0;const e=T.parseResult||Ft(T.exp);e&&c.push(de(t.helper(H),[e.source,he(jt(e),zt(m,S),!0)]))}else{if(v){if(d.has(v))continue;d.add(v),"default"===v&&(u=!0)}s.push(ae(m,S))}}p||(a?f.length&&(u||s.push(i(void 0,f))):s.push(i(void 0,o)));const h=l?2:Jt(e.children)?3:1;let m=pe(s.concat(ae("_",ue(h+"",!1))),r);return c.length&&(m=de(t.helper(K),[m,le(c)])),{slots:m,hasDynamicSlots:l}}function zt(e,t){return pe([ae("name",e),ae("fn",t)])}function Jt(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||0===n.tagType&&Jt(n.children))return!0;break;case 9:if(Jt(n.branches))return!0;break;case 10:case 11:if(Jt(n.children))return!0}}return!1}const qt=new WeakMap,Yt=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType,s=r?Zt(e,t):`"${n}"`;let c,i,l,p,a,u,f=0,d=m(s)&&s.callee===B||s===O||s===C||!r&&("svg"===n||"foreignObject"===n||Ce(e,"key",!0));if(o.length>0){const n=Xt(e,t);c=n.props,f=n.patchFlag,a=n.dynamicPropNames;const o=n.directives;u=o&&o.length?le(o.map((e=>function(e,t){const n=[],o=qt.get(e);o?n.push(t.helperString(o)):(t.helper(D),t.directives.add(e.name),n.push(Le(e.name,"directive")));const{loc:r}=e;e.exp&&n.push(e.exp);e.arg&&(e.exp||n.push("void 0"),n.push(e.arg));if(Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=ue("true",!1,r);n.push(pe(e.modifiers.map((e=>ae(e,t))),r))}return le(n,e.loc)}(e,t)))):void 0}if(e.children.length>0){s===I&&(d=!0,f|=1024);if(r&&s!==O&&s!==I){const{slots:n,hasDynamicSlots:o}=Gt(e,t);i=n,o&&(f|=1024)}else if(1===e.children.length&&s!==O){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===ft(n,t)&&(f|=1),i=r||2===o?n:e.children}else i=e.children}0!==f&&(l=String(f),a&&a.length&&(p=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(a))),e.codegenNode=ie(t,s,c,i,l,p,u,!!d,!1,e.loc)};function Zt(e,t,n=!1){const{tag:o}=e,r=tn(o)?Ce(e,"is"):Oe(e,"is");if(r){const e=6===r.type?r.value&&ue(r.value.content,!0):r.exp;if(e)return de(t.helper(B),[e])}const s=Se(o)||t.isBuiltInComponent(o);return s?(n||t.helper(s),s):(t.helper(A),t.components.add(o),Le(o,"component"))}function Xt(e,t,n=e.props,o=!1){const{tag:r,loc:s}=e,c=1===e.tagType;let i=[];const l=[],p=[];let u=0,f=!1,d=!1,m=!1,y=!1,v=!1,S=!1;const x=[],b=({key:e,value:n})=>{if(ye(e)){const o=e.content,r=(e=>a.test(e))(o);if(c||!r||"onclick"===o.toLowerCase()||"onUpdate:modelValue"===o||g(o)||(y=!0),r&&g(o)&&(S=!0),20===n.type||(4===n.type||8===n.type)&&ft(n,t)>0)return;"ref"===o?f=!0:"class"!==o||c?"style"!==o||c?"key"===o||x.includes(o)||x.push(o):m=!0:d=!0}else v=!0};for(let a=0;a<n.length;a++){const c=n[a];if(6===c.type){const{loc:e,name:t,value:n}=c;let o=!0;if("ref"===t&&(f=!0),"is"===t&&tn(r))continue;i.push(ae(ue(t,!0,Ee(e,0,t.length)),ue(n?n.content:"",o,n?n.loc:e)))}else{const{name:n,arg:a,exp:u,loc:f}=c,d="bind"===n,m="on"===n;if("slot"===n)continue;if("once"===n)continue;if("is"===n||d&&tn(r)&&Ie(a,"is"))continue;if(m&&o)continue;if(!a&&(d||m)){v=!0,u&&(i.length&&(l.push(pe(Qt(i),s)),i=[]),l.push(d?u:{type:14,loc:f,callee:t.helper(G),arguments:[u]}));continue}const g=t.directiveTransforms[n];if(g){const{props:n,needRuntime:r}=g(c,e,t);!o&&n.forEach(b),i.push(...n),r&&(p.push(c),h(r)&&qt.set(c,r))}else p.push(c)}}let T;return l.length?(i.length&&l.push(pe(Qt(i),s)),T=l.length>1?de(t.helper(W),l,s):l[0]):i.length&&(T=pe(Qt(i),s)),v?u|=16:(d&&(u|=2),m&&(u|=4),x.length&&(u|=8),y&&(u|=32)),0!==u&&32!==u||!(f||S||p.length>0)||(u|=512),{props:T,directives:p,patchFlag:u,dynamicPropNames:x}}function Qt(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,c=t.get(s);c?("style"===s||"class"===s||s.startsWith("on"))&&en(c,r):(t.set(s,r),n.push(r))}return n}function en(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=le([e.value,t.value],e.loc)}function tn(e){return e[0].toLowerCase()+e.slice(1)==="component"}const nn=(e,t)=>{if(Ve(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=on(e,t),c=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r];s&&c.push(s),n.length&&(s||c.push("{}"),c.push(he([],n,!1,!1,o))),t.scopeId&&!t.slotted&&(s||c.push("{}"),n.length||c.push("undefined"),c.push("true")),e.codegenNode=de(t.helper(j),c,o)}};function on(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=S(t.name),r.push(t))):"bind"===t.name&&Ie(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&ye(t.arg)&&(t.arg.content=S(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=Xt(e,t,r);n=o}return{slotName:o,slotProps:n}}const rn=/^\s*([\w$_]+|\([^)]*?\))\s*=>|^\s*function(?:\s+[\w$]+)?\s*\(/,sn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:c}=e;let i;if(4===c.type)if(c.isStatic){i=ue(N(S(c.content)),!0,c.loc)}else i=fe([`${n.helperString(q)}(`,c,")"]);else i=c,i.children.unshift(`${n.helperString(q)}(`),i.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let p=n.cacheHandlers&&!l;if(l){const e=Ne(l.content),t=!(e||rn.test(l.content)),n=l.content.includes(";");(t||p&&e)&&(l=fe([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let a={props:[ae(i,l||ue("() => {}",!1,r))]};return o&&(a=o(a)),p&&(a.props[0].value=n.cache(a.props[0].value)),a},cn=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,c=e.arg;return 4!==c.type?(c.children.unshift("("),c.children.push(') || ""')):c.isStatic||(c.content=`${c.content} || ""`),r.includes("camel")&&(4===c.type?c.content=c.isStatic?S(c.content):`${n.helperString(z)}(${c.content})`:(c.children.unshift(`${n.helperString(z)}(`),c.children.push(")"))),!o||4===o.type&&!o.content.trim()?{props:[ae(c,ue("",!0,s))]}:{props:[ae(c,o)]}},ln=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(Me(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!Me(s)){o=void 0;break}o||(o=n[e]={type:8,loc:t.loc,children:[t]}),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType)))for(let e=0;e<n.length;e++){const o=n[e];if(Me(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==ft(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:de(t.helper(R),r)}}}}},pn=new WeakSet,an=(e,t)=>{if(1===e.type&&Oe(e,"once",!0)){if(pn.has(e))return;return pn.add(e),t.helper(Y),()=>{const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},un=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return fn();const s=o.loc.source;if(!Ne(4===o.type?o.content:s))return fn();const c=r||ue("modelValue",!0),i=r?ye(r)?`onUpdate:${r.content}`:fe(['"onUpdate:" + ',r]):"onUpdate:modelValue";let l;l=fe([`${n.isTS?"($event: any)":"$event"} => (`,o," = $event)"]);const p=[ae(c,e.exp),ae(i,l)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(be(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?ye(r)?`${r.content}Modifiers`:fe([r,' + "Modifiers"']):"modelModifiers";p.push(ae(n,ue(`{ ${t} }`,!1,e.loc,2)))}return fn(p)};function fn(e=[]){return{props:e}}function dn(e){return[[an,$t,Rt,nn,Yt,Ut,ln],{on:sn,bind:cn,model:un}]}function hn(e,t={}){const n=t.onError||E,o="module"===t.mode;!0===t.prefixIdentifiers?n(k(45)):o&&n(k(46));t.cacheHandlers&&n(k(47)),t.scopeId&&!o&&n(k(48));const r=d(e)?Fe(e,t):e,[s,c]=dn();return yt(r,u({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:u({},c,t.directiveTransforms||{})})),bt(r,u({},t,{prefixIdentifiers:false}))}const mn=()=>({props:[]}),gn=Symbol(""),yn=Symbol(""),vn=Symbol(""),Sn=Symbol(""),xn=Symbol(""),bn=Symbol(""),Tn=Symbol(""),Nn=Symbol(""),En=Symbol(""),kn=Symbol("");let _n;re({[gn]:"vModelRadio",[yn]:"vModelCheckbox",[vn]:"vModelText",[Sn]:"vModelSelect",[xn]:"vModelDynamic",[bn]:"withModifiers",[Tn]:"withKeys",[Nn]:"vShow",[En]:"Transition",[kn]:"TransitionGroup"});const On=t("style,iframe,script,noscript",!0),Cn={isVoidTag:c,isNativeTag:e=>r(e)||s(e),isPreTag:e=>"pre"===e,decodeEntities:function(e){return(_n||(_n=document.createElement("div"))).innerHTML=e,_n.textContent},isBuiltInComponent:e=>ve(e,"Transition")?En:ve(e,"TransitionGroup")?kn:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(On(e))return 2}return 0}},In=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:ue("style",!0,t.loc),exp:$n(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},$n=(e,t)=>{const r=function(e){const t={};return e.split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return ue(JSON.stringify(r),!1,t,3)};function Mn(e,t){return k(e,t)}const wn=t("passive,once,capture"),Pn=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),Vn=t("left,right"),Rn=t("onkeyup,onkeydown,onkeypress",!0),Ln=(e,t)=>ye(e)&&"onclick"===e.content.toLowerCase()?ue(t,!0):4!==e.type?fe(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,An=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Bn=[In],Dn={cloak:mn,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ae(ue("innerHTML",!0,r),o||ue("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ae(ue("textContent",!0),o?de(n.helperString(U),[o],r):ue("",!0))]}},model:(e,t,n)=>{const o=un(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=vn,c=!1;if("input"===r||s){const n=Ce(t,"type");if(n){if(7===n.type)e=xn;else if(n.value)switch(n.value.content){case"radio":e=gn;break;case"checkbox":e=yn;break;case"file":c=!0}}else $e(t)&&(e=xn)}else"select"===r&&(e=Sn);c||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>sn(e,0,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:c,nonKeyModifiers:i,eventOptionModifiers:l}=((e,t)=>{const n=[],o=[],r=[];for(let s=0;s<t.length;s++){const c=t[s];wn(c)?r.push(c):Vn(c)?ye(e)?Rn(e.content)?n.push(c):o.push(c):(n.push(c),o.push(c)):Pn(c)?o.push(c):n.push(c)}return{keyModifiers:n,nonKeyModifiers:o,eventOptionModifiers:r}})(r,o);if(i.includes("right")&&(r=Ln(r,"onContextmenu")),i.includes("middle")&&(r=Ln(r,"onMouseup")),i.length&&(s=de(n.helper(bn),[s,JSON.stringify(i)])),!c.length||ye(r)&&!Rn(r.content)||(s=de(n.helper(Tn),[s,JSON.stringify(c)])),l.length){const e=l.map(T).join("");r=ye(r)?ue(`${r.content}${e}`,!0):fe(["(",r,`) + "${e}"`])}return{props:[ae(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(Nn)})};return e.BASE_TRANSITION=$,e.CAMELIZE=z,e.CAPITALIZE=J,e.CREATE_BLOCK=w,e.CREATE_COMMENT=V,e.CREATE_SLOTS=K,e.CREATE_STATIC=L,e.CREATE_TEXT=R,e.CREATE_VNODE=P,e.DOMDirectiveTransforms=Dn,e.DOMNodeTransforms=Bn,e.FRAGMENT=_,e.IS_REF=ne,e.KEEP_ALIVE=I,e.MERGE_PROPS=W,e.OPEN_BLOCK=M,e.POP_SCOPE_ID=X,e.PUSH_SCOPE_ID=Z,e.RENDER_LIST=H,e.RENDER_SLOT=j,e.RESOLVE_COMPONENT=A,e.RESOLVE_DIRECTIVE=D,e.RESOLVE_DYNAMIC_COMPONENT=B,e.SET_BLOCK_TRACKING=Y,e.SUSPENSE=C,e.TELEPORT=O,e.TO_DISPLAY_STRING=U,e.TO_HANDLERS=G,e.TO_HANDLER_KEY=q,e.TRANSITION=En,e.TRANSITION_GROUP=kn,e.UNREF=te,e.V_MODEL_CHECKBOX=yn,e.V_MODEL_DYNAMIC=xn,e.V_MODEL_RADIO=gn,e.V_MODEL_SELECT=Sn,e.V_MODEL_TEXT=vn,e.V_ON_WITH_KEYS=Tn,e.V_ON_WITH_MODIFIERS=bn,e.V_SHOW=Nn,e.WITH_CTX=ee,e.WITH_DIRECTIVES=F,e.WITH_SCOPE_ID=Q,e.advancePositionWithClone=ke,e.advancePositionWithMutation=_e,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=hn,e.baseParse=Fe,e.buildProps=Xt,e.buildSlots=Gt,e.compile=function(e,t={}){return hn(e,u({},Cn,t,{nodeTransforms:[An,...Bn,...t.nodeTransforms||[]],directiveTransforms:u({},Dn,t.directiveTransforms||{}),transformHoist:null}))},e.createArrayExpression=le,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:se}},e.createBlockStatement=function(e){return{type:21,body:e,loc:se}},e.createCacheExpression=ge,e.createCallExpression=de,e.createCompilerError=k,e.createCompoundExpression=fe,e.createConditionalExpression=me,e.createDOMCompilerError=Mn,e.createForLoopParams=jt,e.createFunctionExpression=he,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:se}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:d(e)?ue(e,!1,t):e}},e.createObjectExpression=pe,e.createObjectProperty=ae,e.createReturnStatement=function(e){return{type:26,returns:e,loc:se}},e.createRoot=ce,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:se}},e.createSimpleExpression=ue,e.createStructuralDirectiveTransform=St,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:se}},e.createTransformContext=gt,e.createVNodeCall=ie,e.findDir=Oe,e.findProp=Ce,e.generate=bt,e.generateCodeFrame=function(e,t=0,n=e.length){const o=e.split(/\r?\n/);let r=0;const s=[];for(let c=0;c<o.length;c++)if(r+=o[c].length+1,r>=t){for(let e=c-2;e<=c+2||n>r;e++){if(e<0||e>=o.length)continue;const i=e+1;s.push(`${i}${" ".repeat(Math.max(3-String(i).length,0))}|  ${o[e]}`);const l=o[e].length;if(e===c){const e=t-(r-l)+1,o=Math.max(1,n>r?l-e:n-t);s.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>r){const e=Math.max(Math.min(n-r,l),1);s.push("   |  "+"^".repeat(e))}r+=l+1}}break}return s.join("\n")},e.getBaseTransformPreset=dn,e.getInnerRange=Ee,e.hasDynamicKeyVBind=$e,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let o=0;o<t.props.length;o++){const r=t.props[o];if(7===r.type&&(e(r.arg,n)||e(r.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&be(t.content)&&!!n[t.content];case 8:return t.children.some((t=>m(t)&&e(t,n)));case 5:case 12:return e(t.content,n);case 2:case 3:default:return!1}},e.helperNameMap=oe,e.injectProp=Re,e.isBindKey=Ie,e.isBuiltInType=ve,e.isCoreComponent=Se,e.isMemberExpression=Ne,e.isSimpleIdentifier=be,e.isSlotOutlet=Ve,e.isStaticExp=ye,e.isTemplateNode=Pe,e.isText=Me,e.isVSlot=we,e.locStub=se,e.noopDirectiveTransform=mn,e.parse=function(e,t={}){return Fe(e,u({},Cn,t))},e.parserOptions=Cn,e.processExpression=It,e.processFor=Lt,e.processIf=Mt,e.processSlotOutlet=on,e.registerRuntimeHelpers=re,e.resolveComponentType=Zt,e.toValidAssetId=Le,e.trackSlotScopes=Ut,e.trackVForSlotScopes=(e,t)=>{let n;if(Pe(e)&&e.props.some(we)&&(n=Oe(e,"for"))){const e=n.parseResult=Ft(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:c}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&c(n),o&&c(o),r&&c(r)}}}},e.transform=yt,e.transformBind=cn,e.transformElement=Yt,e.transformExpression=(e,t)=>{if(5===e.type)e.content=It(e.content);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=It(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=It(n))}}},e.transformModel=un,e.transformOn=sn,e.transformStyle=In,e.traverseNode=vt,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
