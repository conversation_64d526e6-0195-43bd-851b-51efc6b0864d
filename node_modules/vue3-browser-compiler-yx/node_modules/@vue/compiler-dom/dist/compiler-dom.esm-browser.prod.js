function e(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function t(e,t=0,n=e.length){const o=e.split(/\r?\n/);let r=0;const s=[];for(let c=0;c<o.length;c++)if(r+=o[c].length+1,r>=t){for(let e=c-2;e<=c+2||n>r;e++){if(e<0||e>=o.length)continue;const i=e+1;s.push(`${i}${" ".repeat(Math.max(3-String(i).length,0))}|  ${o[e]}`);const l=o[e].length;if(e===c){const e=t-(r-l)+1,o=Math.max(1,n>r?l-e:n-t);s.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>r){const e=Math.max(Math.min(n-r,l),1);s.push("   |  "+"^".repeat(e))}r+=l+1}}break}return s.join("\n")}const n=/;(?![^(]*\))/g,o=/:(.+)/;const r=e("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),s=e("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),c=e("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),i={},l=()=>{},p=()=>!1,u=/^on[^a-z]/,a=Object.assign,f=Array.isArray,d=e=>"string"==typeof e,h=e=>"symbol"==typeof e,m=e=>null!==e&&"object"==typeof e,g=e(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),y=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},v=/-(\w)/g,b=y((e=>e.replace(v,((e,t)=>t?t.toUpperCase():"")))),x=/\B([A-Z])/g,S=y((e=>e.replace(x,"-$1").toLowerCase())),k=y((e=>e.charAt(0).toUpperCase()+e.slice(1))),N=y((e=>e?`on${k(e)}`:""));function T(e){throw e}function $(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const w=Symbol(""),_=Symbol(""),C=Symbol(""),M=Symbol(""),O=Symbol(""),I=Symbol(""),P=Symbol(""),V=Symbol(""),F=Symbol(""),B=Symbol(""),L=Symbol(""),E=Symbol(""),A=Symbol(""),j=Symbol(""),H=Symbol(""),R=Symbol(""),D=Symbol(""),U=Symbol(""),z=Symbol(""),J=Symbol(""),G=Symbol(""),q=Symbol(""),K=Symbol(""),W=Symbol(""),Z=Symbol(""),Q=Symbol(""),Y=Symbol(""),X=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe={[w]:"Fragment",[_]:"Teleport",[C]:"Suspense",[M]:"KeepAlive",[O]:"BaseTransition",[I]:"openBlock",[P]:"createBlock",[V]:"createVNode",[F]:"createCommentVNode",[B]:"createTextVNode",[L]:"createStaticVNode",[E]:"resolveComponent",[A]:"resolveDynamicComponent",[j]:"resolveDirective",[H]:"withDirectives",[R]:"renderList",[D]:"renderSlot",[U]:"createSlots",[z]:"toDisplayString",[J]:"mergeProps",[G]:"toHandlers",[q]:"camelize",[K]:"capitalize",[W]:"toHandlerKey",[Z]:"setBlockTracking",[Q]:"pushScopeId",[Y]:"popScopeId",[X]:"withScopeId",[ee]:"withCtx",[te]:"unref",[ne]:"isRef"};function re(e){Object.getOwnPropertySymbols(e).forEach((t=>{oe[t]=e[t]}))}const se={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ce(e,t=se){return{type:0,children:e,helpers:[],components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function ie(e,t,n,o,r,s,c,i=!1,l=!1,p=se){return e&&(i?(e.helper(I),e.helper(P)):e.helper(V),c&&e.helper(H)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:c,isBlock:i,disableTracking:l,loc:p}}function le(e,t=se){return{type:17,loc:t,elements:e}}function pe(e,t=se){return{type:15,loc:t,properties:e}}function ue(e,t){return{type:16,loc:se,key:d(e)?ae(e,!0):e,value:t}}function ae(e,t,n=se,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function fe(e,t){return{type:5,loc:t,content:d(e)?ae(e,!1,t):e}}function de(e,t=se){return{type:8,loc:t,children:e}}function he(e,t=[],n=se){return{type:14,loc:n,callee:e,arguments:t}}function me(e,t,n=!1,o=!1,r=se){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function ge(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:se}}function ye(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:se}}function ve(e){return{type:21,body:e,loc:se}}function be(e){return{type:22,elements:e,loc:se}}function xe(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:se}}function Se(e,t){return{type:24,left:e,right:t,loc:se}}function ke(e){return{type:25,expressions:e,loc:se}}function Ne(e){return{type:26,returns:e,loc:se}}const Te=e=>4===e.type&&e.isStatic,$e=(e,t)=>e===t||e===S(t);function we(e){return $e(e,"Teleport")?_:$e(e,"Suspense")?C:$e(e,"KeepAlive")?M:$e(e,"BaseTransition")?O:void 0}const _e=/^\d|[^\$\w]/,Ce=e=>!_e.test(e),Me=/^[A-Za-z_$\xA0-\uFFFF][\w$\xA0-\uFFFF]*(?:\s*\.\s*[A-Za-z_$\xA0-\uFFFF][\w$\xA0-\uFFFF]*|\[[^\]]+\])*$/,Oe=e=>!!e&&Me.test(e.trim());function Ie(e,t,n){const o={source:e.source.substr(t,n),start:Pe(e.start,e.source,t),end:e.end};return null!=n&&(o.end=Pe(e.start,e.source,t+n)),o}function Pe(e,t,n=t.length){return Ve(a({},e),t,n)}function Ve(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Fe(e,t){if(!e)throw new Error(t||"unexpected compiler condition")}function Be(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(d(t)?r.name===t:t.test(r.name)))return r}}function Le(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Ee(s.arg,t))return s}}function Ee(e,t){return!(!e||!Te(e)||e.content!==t)}function Ae(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function je(e){return 5===e.type||2===e.type}function He(e){return 7===e.type&&"slot"===e.name}function Re(e){return 1===e.type&&3===e.tagType}function De(e){return 1===e.type&&2===e.tagType}function Ue(e,t,n){let o;const r=13===e.type?e.props:e.arguments[2];if(null==r||d(r))o=pe([t]);else if(14===r.type){const e=r.arguments[0];d(e)||15!==e.type?r.callee===G?o=he(n.helper(J),[pe([t]),r]):r.arguments.unshift(pe([t])):e.properties.unshift(t),!o&&(o=r)}else if(15===r.type){let e=!1;if(4===t.key.type){const n=t.key.content;e=r.properties.some((e=>4===e.key.type&&e.key.content===n))}e||r.properties.unshift(t),o=r}else o=he(n.helper(J),[pe([t]),r]);13===e.type?e.props=o:e.arguments[2]=o}function ze(e,t){return`_${t}_${e.replace(/[^\w]/g,"_")}`}function Je(e,t){if(!e||0===Object.keys(t).length)return!1;switch(e.type){case 1:for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&(Je(o.arg,t)||Je(o.exp,t)))return!0}return e.children.some((e=>Je(e,t)));case 11:return!!Je(e.source,t)||e.children.some((e=>Je(e,t)));case 9:return e.branches.some((e=>Je(e,t)));case 10:return!!Je(e.condition,t)||e.children.some((e=>Je(e,t)));case 4:return!e.isStatic&&Ce(e.content)&&!!t[e.content];case 8:return e.children.some((e=>m(e)&&Je(e,t)));case 5:case 12:return Je(e.content,t);case 2:case 3:default:return!1}}const Ge=/&(gt|lt|amp|apos|quot);/g,qe={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},Ke={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:p,isPreTag:p,isCustomElement:p,decodeEntities:e=>e.replace(Ge,((e,t)=>qe[t])),onError:T,comments:!1};function We(e,t={}){const n=function(e,t){const n=a({},Ke);for(const o in t)n[o]=t[o]||Ke[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1}}(e,t),o=pt(n);return ce(Ze(n,0,[]),ut(n,o))}function Ze(e,t,n){const o=at(n),r=o?o.ns:0,s=[];for(;!gt(e,t,n);){const c=e.source;let i;if(0===t||1===t)if(!e.inVPre&&ft(c,e.options.delimiters[0]))i=ct(e,t);else if(0===t&&"<"===c[0])if(1===c.length);else if("!"===c[1])i=ft(c,"\x3c!--")?Xe(e):ft(c,"<!DOCTYPE")?et(e):ft(c,"<![CDATA[")&&0!==r?Ye(e,n):et(e);else if("/"===c[1])if(2===c.length);else{if(">"===c[2]){dt(e,3);continue}if(/[a-z]/i.test(c[2])){ot(e,1,o);continue}i=et(e)}else/[a-z]/i.test(c[1])?i=tt(e,n):"?"===c[1]&&(i=et(e));if(i||(i=it(e,t)),f(i))for(let e=0;e<i.length;e++)Qe(s,i[e]);else Qe(s,i)}let c=!1;if(2!==t&&1!==t){for(let t=0;t<s.length;t++){const n=s[t];if(!e.inPre&&2===n.type)if(/[^\t\r\n\f ]/.test(n.content))n.content=n.content.replace(/[\t\r\n\f ]+/g," ");else{const e=s[t-1],o=s[t+1];!e||!o||3===e.type||3===o.type||1===e.type&&1===o.type&&/[\r\n]/.test(n.content)?(c=!0,s[t]=null):n.content=" "}3!==n.type||e.options.comments||(c=!0,s[t]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return c?s.filter(Boolean):s}function Qe(e,t){if(2===t.type){const n=at(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function Ye(e,t){dt(e,9);const n=Ze(e,3,t);return 0===e.source.length||dt(e,3),n}function Xe(e){const t=pt(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)dt(e,s-r+1),r=s+1;dt(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),dt(e,e.source.length);return{type:3,content:n,loc:ut(e,t)}}function et(e){const t=pt(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),dt(e,e.source.length)):(o=e.source.slice(n,r),dt(e,r+1)),{type:3,content:o,loc:ut(e,t)}}function tt(e,t){const n=e.inPre,o=e.inVPre,r=at(t),s=ot(e,0,r),c=e.inPre&&!n,i=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return s;t.push(s);const l=e.options.getTextMode(s,r),p=Ze(e,l,t);if(t.pop(),s.children=p,yt(e.source,s.tag))ot(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=p[0];e&&ft(e.loc.source,"\x3c!--")}return s.loc=ut(e,s.loc.start),c&&(e.inPre=!1),i&&(e.inVPre=!1),s}const nt=e("if,else,else-if,for,slot");function ot(e,t,n){const o=pt(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],c=e.options.getNamespace(s,n);dt(e,r[0].length),ht(e);const i=pt(e),l=e.source;let p=rt(e,t);e.options.isPreTag(s)&&(e.inPre=!0),!e.inVPre&&p.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,a(e,i),e.source=l,p=rt(e,t).filter((e=>"v-pre"!==e.name)));let u=!1;0===e.source.length||(u=ft(e.source,"/>"),dt(e,u?2:1));let f=0;const d=e.options;if(!e.inVPre&&!d.isCustomElement(s)){const e=p.some((e=>7===e.type&&"is"===e.name));d.isNativeTag&&!e?d.isNativeTag(s)||(f=1):(e||we(s)||d.isBuiltInComponent&&d.isBuiltInComponent(s)||/^[A-Z]/.test(s)||"component"===s)&&(f=1),"slot"===s?f=2:"template"===s&&p.some((e=>7===e.type&&nt(e.name)))&&(f=3)}return{type:1,ns:c,tag:s,tagType:f,props:p,isSelfClosing:u,children:[],loc:ut(e,o),codegenNode:void 0}}function rt(e,t){const n=[],o=new Set;for(;e.source.length>0&&!ft(e.source,">")&&!ft(e.source,"/>");){if(ft(e.source,"/")){dt(e,1),ht(e);continue}const r=st(e,o);0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),ht(e)}return n}function st(e,t){const n=pt(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;dt(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(ht(e),dt(e,1),ht(e),r=function(e){const t=pt(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){dt(e,1);const t=e.source.indexOf(o);-1===t?n=lt(e,e.source.length,4):(n=lt(e,t,4),dt(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=lt(e,t[0].length,4)}return{content:n,isQuoted:r,loc:ut(e,t)}}(e));const s=ut(e,n);if(!e.inVPre&&/^(v-|:|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o),c=t[1]||(ft(o,":")?"bind":ft(o,"@")?"on":"slot");let i;if(t[2]){const r="slot"===c,s=o.lastIndexOf(t[2]),l=ut(e,mt(e,n,s),mt(e,n,s+t[2].length+(r&&t[3]||"").length));let p=t[2],u=!0;p.startsWith("[")?(u=!1,p.endsWith("]"),p=p.substr(1,p.length-2)):r&&(p+=t[3]||""),i={type:4,content:p,isStatic:u,constType:u?3:0,loc:l}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=Pe(e.start,r.content),e.source=e.source.slice(1,-1)}return{type:7,name:c,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:t[3]?t[3].substr(1).split("."):[],loc:s}}return{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function ct(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=pt(e);dt(e,n.length);const c=pt(e),i=pt(e),l=r-n.length,p=e.source.slice(0,l),u=lt(e,l,t),a=u.trim(),f=u.indexOf(a);f>0&&Ve(c,p,f);return Ve(i,p,l-(u.length-a.length-f)),dt(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:a,loc:ut(e,c,i)},loc:ut(e,s)}}function it(e,t){const n=["<",e.options.delimiters[0]];3===t&&n.push("]]>");let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=pt(e);return{type:2,content:lt(e,o,t),loc:ut(e,r)}}function lt(e,t,n){const o=e.source.slice(0,t);return dt(e,t),2===n||3===n||-1===o.indexOf("&")?o:e.options.decodeEntities(o,4===n)}function pt(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function ut(e,t,n){return{start:t,end:n=n||pt(e),source:e.originalSource.slice(t.offset,n.offset)}}function at(e){return e[e.length-1]}function ft(e,t){return e.startsWith(t)}function dt(e,t){const{source:n}=e;Ve(e,n,t),e.source=n.slice(t)}function ht(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&dt(e,t[0].length)}function mt(e,t,n){return Pe(t,e.originalSource.slice(t.offset,n),n)}function gt(e,t,n){const o=e.source;switch(t){case 0:if(ft(o,"</"))for(let e=n.length-1;e>=0;--e)if(yt(o,n[e].tag))return!0;break;case 1:case 2:{const e=at(n);if(e&&yt(o,e.tag))return!0;break}case 3:if(ft(o,"]]>"))return!0}return!o}function yt(e,t){return ft(e,"</")&&e.substr(2,t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function vt(e,t){xt(e,t,bt(e,e.children[0]))}function bt(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!De(t)}function xt(e,t,n=!1){let o=!1,r=!0;const{children:s}=e;for(let c=0;c<s.length;c++){const e=s[c];if(1===e.type&&0===e.tagType){const s=n?0:St(e,t);if(s>0){if(s<3&&(r=!1),s>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),o=!0;continue}}else{const n=e.codegenNode;if(13===n.type){const o=Tt(n);if((!o||512===o||1===o)&&kt(e,t)>=2){const o=Nt(e);o&&(n.props=t.hoist(o))}}}}else if(12===e.type){const n=St(e.content,t);n>0&&(n<3&&(r=!1),n>=2&&(e.codegenNode=t.hoist(e.codegenNode),o=!0))}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,xt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)xt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)xt(e.branches[n],t,1===e.branches[n].children.length)}r&&o&&t.transformHoist&&t.transformHoist(s,t,e)}function St(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(Tt(r))return n.set(e,0),0;{let o=3;const s=kt(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=St(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=St(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}return r.isBlock&&(t.removeHelper(I),t.removeHelper(P),r.isBlock=!1,t.helper(V)),n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:return 0;case 5:case 12:return St(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(d(o)||h(o))continue;const r=St(o,t);if(0===r)return 0;r<s&&(s=r)}return s;default:return 0}}function kt(e,t){let n=3;const o=Nt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],c=St(r,t);if(0===c)return c;if(c<n&&(n=c),4!==s.type)return 0;const i=St(s,t);if(0===i)return i;i<n&&(n=i)}}return n}function Nt(e){const t=e.codegenNode;if(13===t.type)return t.props}function Tt(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function $t(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:c={},transformHoist:p=null,isBuiltInComponent:u=l,isCustomElement:a=l,expressionPlugins:f=[],scopeId:d=null,slotted:h=!0,ssr:m=!1,ssrCssVars:g="",bindingMetadata:y=i,inline:v=!1,isTS:x=!1,onError:S=T}){const N=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),$={selfName:N&&k(b(N[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:c,transformHoist:p,isBuiltInComponent:u,isCustomElement:a,expressionPlugins:f,scopeId:d,slotted:h,ssr:m,ssrCssVars:g,bindingMetadata:y,inline:v,isTS:x,onError:S,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,helper(e){const t=$.helpers.get(e)||0;return $.helpers.set(e,t+1),e},removeHelper(e){const t=$.helpers.get(e);if(t){const n=t-1;n?$.helpers.set(e,n):$.helpers.delete(e)}},helperString:e=>`_${oe[$.helper(e)]}`,replaceNode(e){$.parent.children[$.childIndex]=$.currentNode=e},removeNode(e){const t=e?$.parent.children.indexOf(e):$.currentNode?$.childIndex:-1;e&&e!==$.currentNode?$.childIndex>t&&($.childIndex--,$.onNodeRemoved()):($.currentNode=null,$.onNodeRemoved()),$.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){$.hoists.push(e);const t=ae(`_hoisted_${$.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>ye(++$.cached,e,t)};return $}function wt(e,t){const n=$t(e,t);_t(e,n),t.hoistStatic&&vt(e,n),t.ssr||function(e,t){const{helper:n,removeHelper:o}=t,{children:r}=e;if(1===r.length){const t=r[0];if(bt(e,t)&&t.codegenNode){const r=t.codegenNode;13===r.type&&(r.isBlock||(o(V),r.isBlock=!0,n(I),n(P))),e.codegenNode=r}else e.codegenNode=t}else if(r.length>1){let o=64;e.codegenNode=ie(t,n(w),void 0,e.children,o+"",void 0,void 0,!0)}}(e,n),e.helpers=[...n.helpers.keys()],e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached}function _t(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(f(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(F);break;case 5:t.ssr||t.helper(z);break;case 9:for(let n=0;n<e.branches.length;n++)_t(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];d(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,_t(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function Ct(e,t){const n=d(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(He))return;const s=[];for(let c=0;c<r.length;c++){const i=r[c];if(7===i.type&&n(i.name)){r.splice(c,1),c--;const n=t(e,i,o);n&&s.push(n)}}return s}}}function Mt(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:c=!1,runtimeGlobalName:i="Vue",runtimeModuleName:l="vue",ssr:p=!1}){const u={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:c,runtimeGlobalName:i,runtimeModuleName:l,ssr:p,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${oe[e]}`,push(e,t){u.code+=e},indent(){a(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:a(--u.indentLevel)},newline(){a(u.indentLevel)}};function a(e){u.push("\n"+"  ".repeat(e))}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:c,deindent:i,newline:l,ssr:p}=n,u=e.helpers.length>0,a=!s&&"module"!==o;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,c=e=>`${oe[e]}: _${oe[e]}`;if(e.helpers.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[V,F,B,L].filter((t=>e.helpers.includes(t))).map(c).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o(),e.forEach(((e,r)=>{e&&(n(`const _hoisted_${r+1} = `),Vt(e,t),o())})),t.pure=!1})(e.hoists,t),o(),n("return ")}(e,n);if(r(`function ${p?"ssrRender":"render"}(${(p?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),c(),a&&(r("with (_ctx) {"),c(),u&&(r(`const { ${e.helpers.map((e=>`${oe[e]}: _${oe[e]}`)).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(Ot(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Ot(e.directives,"directive",n),e.temps>0&&l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),p||r("return "),e.codegenNode?Vt(e.codegenNode,n):r("null"),a&&(i(),r("}")),i(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function Ot(e,t,{helper:n,push:o,newline:r}){const s=n("component"===t?E:j);for(let c=0;c<e.length;c++){let n=e[c];const i=n.endsWith("__self");i&&(n=n.slice(0,-6)),o(`const ${ze(n,t)} = ${s}(${JSON.stringify(n)}${i?", true":""})`),c<e.length-1&&r()}}function It(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),Pt(e,t,n),n&&t.deindent(),t.push("]")}function Pt(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let c=0;c<e.length;c++){const i=e[c];d(i)?r(i):f(i)?It(i,t):Vt(i,t),c<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function Vt(e,t){if(d(e))t.push(e);else if(h(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:Vt(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:Ft(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n("/*#__PURE__*/");n(`${o(z)}(`),Vt(e.content,t),n(")")}(e,t);break;case 12:Vt(e.codegenNode,t);break;case 8:Bt(e,t);break;case 3:break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:c,children:i,patchFlag:l,dynamicProps:p,directives:u,isBlock:a,disableTracking:f}=e;u&&n(o(H)+"(");a&&n(`(${o(I)}(${f?"true":""}), `);r&&n("/*#__PURE__*/");n(o(a?P:V)+"(",e),Pt(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,c,i,l,p]),t),n(")"),a&&n(")");u&&(n(", "),Vt(u,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=d(e.callee)?e.callee:o(e.callee);r&&n("/*#__PURE__*/");n(s+"(",e),Pt(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:c}=e;if(!c.length)return void n("{}",e);const i=c.length>1||!1;n(i?"{":"{ "),i&&o();for(let l=0;l<c.length;l++){const{key:e,value:o}=c[l];Lt(e,t),n(": "),Vt(o,t),l<c.length-1&&(n(","),s())}i&&r(),n(i?"}":" }")}(e,t);break;case 17:!function(e,t){It(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:c,body:i,newline:l,isSlot:p}=e;p&&n(`_${oe[ee]}(`);n("(",e),f(s)?Pt(s,t):s&&Vt(s,t);n(") => "),(l||i)&&(n("{"),o());c?(l&&n("return "),f(c)?It(c,t):Vt(c,t)):i&&Vt(i,t);(l||i)&&(r(),n("}"));p&&n(")")}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:c,indent:i,deindent:l,newline:p}=t;if(4===n.type){const e=!Ce(n.content);e&&c("("),Ft(n,t),e&&c(")")}else c("("),Vt(n,t),c(")");s&&i(),t.indentLevel++,s||c(" "),c("? "),Vt(o,t),t.indentLevel--,s&&p(),s||c(" "),c(": ");const u=19===r.type;u||t.indentLevel++;Vt(r,t),u||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:c}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(Z)}(-1),`),c());n(`_cache[${e.index}] = `),Vt(e.value,t),e.isVNode&&(n(","),c(),n(`${o(Z)}(1),`),c(),n(`_cache[${e.index}]`),s());n(")")}(e,t)}}function Ft(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function Bt(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];d(o)?t.push(o):Vt(o,t)}}function Lt(e,t){const{push:n}=t;if(8===e.type)n("["),Bt(e,t),n("]");else if(e.isStatic){n(Ce(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}const Et=(e,t)=>{if(5===e.type)e.content=At(e.content);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=At(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=At(n))}}};function At(e,t,n=!1,o=!1){return e}const jt=Ct(/^(if|else|else-if)$/,((e,t,n)=>Ht(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),c=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(c+=e.branches.length)}return()=>{if(o)e.codegenNode=Dt(t,c,n);else{(function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode)).alternate=Dt(t,c+e.branches.length-1,n)}}}))));function Ht(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=ae("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=Rt(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const c=r[s];if(!c||2!==c.type||c.content.trim().length){if(c&&9===c.type){n.removeNode();const r=Rt(e,t);c.branches.push(r);const s=o&&o(c,r,!1);_t(r,n),s&&s(),n.currentNode=null}break}n.removeNode(c)}}}function Rt(e,t){return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:3!==e.tagType||Be(e,"for")?[e]:e.children,userKey:Le(e,"key")}}function Dt(e,t,n){return e.condition?ge(e.condition,Ut(e,t,n),he(n.helper(F),['""',"true"])):Ut(e,t,n)}function Ut(e,t,n){const{helper:o,removeHelper:r}=n,s=ue("key",ae(`${t}`,!1,se,2)),{children:c}=e,i=c[0];if(1!==c.length||1!==i.type){if(1===c.length&&11===i.type){const e=i.codegenNode;return Ue(e,s,n),e}{let t=64;return ie(n,o(w),pe([s]),c,t+"",void 0,void 0,!0,!1,e.loc)}}{const e=i.codegenNode;return 13!==e.type||e.isBlock||(r(V),e.isBlock=!0,o(I),o(P)),Ue(e,s,n),e}}const zt=Ct("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return Jt(e,t,n,(t=>{const s=he(o(R),[t.source]),c=Le(e,"key"),i=c?ue("key",6===c.type?ae(c.value.content,!0):c.exp):null,l=4===t.source.type&&t.source.constType>0,p=l?64:c?128:256;return t.codegenNode=ie(n,o(w),void 0,s,p+"",void 0,void 0,!0,!l,e.loc),()=>{let c;const p=Re(e),{children:u}=t,a=1!==u.length||1!==u[0].type,f=De(e)?e:p&&1===e.children.length&&De(e.children[0])?e.children[0]:null;f?(c=f.codegenNode,p&&i&&Ue(c,i,n)):a?c=ie(n,o(w),i?pe([i]):void 0,e.children,"64",void 0,void 0,!0):(c=u[0].codegenNode,p&&i&&Ue(c,i,n),c.isBlock!==!l&&(c.isBlock?(r(I),r(P)):r(V)),c.isBlock=!l,c.isBlock?(o(I),o(P)):o(V)),s.arguments.push(me(Qt(t.parseResult),c,!0))}}))}));function Jt(e,t,n,o){if(!t.exp)return;const r=Wt(t.exp);if(!r)return;const{scopes:s}=n,{source:c,value:i,key:l,index:p}=r,u={type:11,loc:t.loc,source:c,valueAlias:i,keyAlias:l,objectIndexAlias:p,parseResult:r,children:Re(e)?e.children:[e]};n.replaceNode(u),s.vFor++;const a=o&&o(u);return()=>{s.vFor--,a&&a()}}const Gt=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,qt=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Kt=/^\(|\)$/g;function Wt(e,t){const n=e.loc,o=e.content,r=o.match(Gt);if(!r)return;const[,s,c]=r,i={source:Zt(n,c.trim(),o.indexOf(c,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(Kt,"").trim();const p=s.indexOf(l),u=l.match(qt);if(u){l=l.replace(qt,"").trim();const e=u[1].trim();let t;if(e&&(t=o.indexOf(e,p+l.length),i.key=Zt(n,e,t)),u[2]){const r=u[2].trim();r&&(i.index=Zt(n,r,o.indexOf(r,i.key?t+e.length:p+l.length)))}}return l&&(i.value=Zt(n,l,p)),i}function Zt(e,t,n){return ae(t,!1,Ie(e,n,t.length))}function Qt({value:e,key:t,index:n}){const o=[];return e&&o.push(e),t&&(e||o.push(ae("_",!1)),o.push(t)),n&&(t||(e||o.push(ae("_",!1)),o.push(ae("__",!1))),o.push(n)),o}const Yt=ae("undefined",!1),Xt=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Be(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},en=(e,t)=>{let n;if(Re(e)&&e.props.some(He)&&(n=Be(e,"for"))){const e=n.parseResult=Wt(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:c}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&c(n),o&&c(o),r&&c(r)}}}},tn=(e,t,n)=>me(e,t,!1,!0,t.length?t[0].loc:n);function nn(e,t,n=tn){t.helper(ee);const{children:o,loc:r}=e,s=[],c=[],i=(e,t)=>ue("default",n(e,t,r));let l=t.scopes.vSlot>0||t.scopes.vFor>0;const p=Be(e,"slot",!0);if(p){const{arg:e,exp:t}=p;e&&!Te(e)&&(l=!0),s.push(ue(e||ae("default",!0),n(t,o,r)))}let u=!1,a=!1;const f=[],d=new Set;for(let g=0;g<o.length;g++){const e=o[g];let r;if(!Re(e)||!(r=Be(e,"slot",!0))){3!==e.type&&f.push(e);continue}if(p)break;u=!0;const{children:i,loc:h}=e,{arg:m=ae("default",!0),exp:y}=r;let v;Te(m)?v=m?m.content:"default":l=!0;const b=n(y,i,h);let x,S,k;if(x=Be(e,"if"))l=!0,c.push(ge(x.exp,on(m,b),Yt));else if(S=Be(e,/^else(-if)?$/,!0)){let e,t=g;for(;t--&&(e=o[t],3===e.type););if(e&&Re(e)&&Be(e,"if")){o.splice(g,1),g--;let e=c[c.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=S.exp?ge(S.exp,on(m,b),Yt):on(m,b)}}else if(k=Be(e,"for")){l=!0;const e=k.parseResult||Wt(k.exp);e&&c.push(he(t.helper(R),[e.source,me(Qt(e),on(m,b),!0)]))}else{if(v){if(d.has(v))continue;d.add(v),"default"===v&&(a=!0)}s.push(ue(m,b))}}p||(u?f.length&&(a||s.push(i(void 0,f))):s.push(i(void 0,o)));const h=l?2:rn(e.children)?3:1;let m=pe(s.concat(ue("_",ae(h+"",!1))),r);return c.length&&(m=he(t.helper(U),[m,le(c)])),{slots:m,hasDynamicSlots:l}}function on(e,t){return pe([ue("name",e),ue("fn",t)])}function rn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||0===n.tagType&&rn(n.children))return!0;break;case 9:if(rn(n.branches))return!0;break;case 10:case 11:if(rn(n.children))return!0}}return!1}const sn=new WeakMap,cn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType,s=r?ln(e,t):`"${n}"`;let c,i,l,p,u,a,f=0,d=m(s)&&s.callee===A||s===_||s===C||!r&&("svg"===n||"foreignObject"===n||Le(e,"key",!0));if(o.length>0){const n=pn(e,t);c=n.props,f=n.patchFlag,u=n.dynamicPropNames;const o=n.directives;a=o&&o.length?le(o.map((e=>function(e,t){const n=[],o=sn.get(e);o?n.push(t.helperString(o)):(t.helper(j),t.directives.add(e.name),n.push(ze(e.name,"directive")));const{loc:r}=e;e.exp&&n.push(e.exp);e.arg&&(e.exp||n.push("void 0"),n.push(e.arg));if(Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=ae("true",!1,r);n.push(pe(e.modifiers.map((e=>ue(e,t))),r))}return le(n,e.loc)}(e,t)))):void 0}if(e.children.length>0){s===M&&(d=!0,f|=1024);if(r&&s!==_&&s!==M){const{slots:n,hasDynamicSlots:o}=nn(e,t);i=n,o&&(f|=1024)}else if(1===e.children.length&&s!==_){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===St(n,t)&&(f|=1),i=r||2===o?n:e.children}else i=e.children}0!==f&&(l=String(f),u&&u.length&&(p=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(u))),e.codegenNode=ie(t,s,c,i,l,p,a,!!d,!1,e.loc)};function ln(e,t,n=!1){const{tag:o}=e,r=fn(o)?Le(e,"is"):Be(e,"is");if(r){const e=6===r.type?r.value&&ae(r.value.content,!0):r.exp;if(e)return he(t.helper(A),[e])}const s=we(o)||t.isBuiltInComponent(o);return s?(n||t.helper(s),s):(t.helper(E),t.components.add(o),ze(o,"component"))}function pn(e,t,n=e.props,o=!1){const{tag:r,loc:s}=e,c=1===e.tagType;let i=[];const l=[],p=[];let a=0,f=!1,d=!1,m=!1,y=!1,v=!1,b=!1;const x=[],S=({key:e,value:n})=>{if(Te(e)){const o=e.content,r=(e=>u.test(e))(o);if(c||!r||"onclick"===o.toLowerCase()||"onUpdate:modelValue"===o||g(o)||(y=!0),r&&g(o)&&(b=!0),20===n.type||(4===n.type||8===n.type)&&St(n,t)>0)return;"ref"===o?f=!0:"class"!==o||c?"style"!==o||c?"key"===o||x.includes(o)||x.push(o):m=!0:d=!0}else v=!0};for(let u=0;u<n.length;u++){const c=n[u];if(6===c.type){const{loc:e,name:t,value:n}=c;let o=!0;if("ref"===t&&(f=!0),"is"===t&&fn(r))continue;i.push(ue(ae(t,!0,Ie(e,0,t.length)),ae(n?n.content:"",o,n?n.loc:e)))}else{const{name:n,arg:u,exp:a,loc:f}=c,d="bind"===n,m="on"===n;if("slot"===n)continue;if("once"===n)continue;if("is"===n||d&&fn(r)&&Ee(u,"is"))continue;if(m&&o)continue;if(!u&&(d||m)){v=!0,a&&(i.length&&(l.push(pe(un(i),s)),i=[]),l.push(d?a:{type:14,loc:f,callee:t.helper(G),arguments:[a]}));continue}const g=t.directiveTransforms[n];if(g){const{props:n,needRuntime:r}=g(c,e,t);!o&&n.forEach(S),i.push(...n),r&&(p.push(c),h(r)&&sn.set(c,r))}else p.push(c)}}let k;return l.length?(i.length&&l.push(pe(un(i),s)),k=l.length>1?he(t.helper(J),l,s):l[0]):i.length&&(k=pe(un(i),s)),v?a|=16:(d&&(a|=2),m&&(a|=4),x.length&&(a|=8),y&&(a|=32)),0!==a&&32!==a||!(f||b||p.length>0)||(a|=512),{props:k,directives:p,patchFlag:a,dynamicPropNames:x}}function un(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,c=t.get(s);c?("style"===s||"class"===s||s.startsWith("on"))&&an(c,r):(t.set(s,r),n.push(r))}return n}function an(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=le([e.value,t.value],e.loc)}function fn(e){return e[0].toLowerCase()+e.slice(1)==="component"}const dn=(e,t)=>{if(De(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=hn(e,t),c=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r];s&&c.push(s),n.length&&(s||c.push("{}"),c.push(me([],n,!1,!1,o))),t.scopeId&&!t.slotted&&(s||c.push("{}"),n.length||c.push("undefined"),c.push("true")),e.codegenNode=he(t.helper(D),c,o)}};function hn(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=b(t.name),r.push(t))):"bind"===t.name&&Ee(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&Te(t.arg)&&(t.arg.content=b(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=pn(e,t,r);n=o}return{slotName:o,slotProps:n}}const mn=/^\s*([\w$_]+|\([^)]*?\))\s*=>|^\s*function(?:\s+[\w$]+)?\s*\(/,gn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:c}=e;let i;if(4===c.type)if(c.isStatic){i=ae(N(b(c.content)),!0,c.loc)}else i=de([`${n.helperString(W)}(`,c,")"]);else i=c,i.children.unshift(`${n.helperString(W)}(`),i.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let p=n.cacheHandlers&&!l;if(l){const e=Oe(l.content),t=!(e||mn.test(l.content)),n=l.content.includes(";");(t||p&&e)&&(l=de([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let u={props:[ue(i,l||ae("() => {}",!1,r))]};return o&&(u=o(u)),p&&(u.props[0].value=n.cache(u.props[0].value)),u},yn=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,c=e.arg;return 4!==c.type?(c.children.unshift("("),c.children.push(') || ""')):c.isStatic||(c.content=`${c.content} || ""`),r.includes("camel")&&(4===c.type?c.content=c.isStatic?b(c.content):`${n.helperString(q)}(${c.content})`:(c.children.unshift(`${n.helperString(q)}(`),c.children.push(")"))),!o||4===o.type&&!o.content.trim()?{props:[ue(c,ae("",!0,s))]}:{props:[ue(c,o)]}},vn=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(je(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!je(s)){o=void 0;break}o||(o=n[e]={type:8,loc:t.loc,children:[t]}),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType)))for(let e=0;e<n.length;e++){const o=n[e];if(je(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==St(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:he(t.helper(B),r)}}}}},bn=new WeakSet,xn=(e,t)=>{if(1===e.type&&Be(e,"once",!0)){if(bn.has(e))return;return bn.add(e),t.helper(Z),()=>{const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Sn=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return kn();const s=o.loc.source;if(!Oe(4===o.type?o.content:s))return kn();const c=r||ae("modelValue",!0),i=r?Te(r)?`onUpdate:${r.content}`:de(['"onUpdate:" + ',r]):"onUpdate:modelValue";let l;l=de([`${n.isTS?"($event: any)":"$event"} => (`,o," = $event)"]);const p=[ue(c,e.exp),ue(i,l)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Ce(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?Te(r)?`${r.content}Modifiers`:de([r,' + "Modifiers"']):"modelModifiers";p.push(ue(n,ae(`{ ${t} }`,!1,e.loc,2)))}return kn(p)};function kn(e=[]){return{props:e}}function Nn(e){return[[xn,jt,zt,dn,cn,Xt,vn],{on:gn,bind:yn,model:Sn}]}function Tn(e,t={}){const n=t.onError||T,o="module"===t.mode;!0===t.prefixIdentifiers?n($(45)):o&&n($(46));t.cacheHandlers&&n($(47)),t.scopeId&&!o&&n($(48));const r=d(e)?We(e,t):e,[s,c]=Nn();return wt(r,a({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:a({},c,t.directiveTransforms||{})})),Mt(r,a({},t,{prefixIdentifiers:false}))}const $n=()=>({props:[]}),wn=Symbol(""),_n=Symbol(""),Cn=Symbol(""),Mn=Symbol(""),On=Symbol(""),In=Symbol(""),Pn=Symbol(""),Vn=Symbol(""),Fn=Symbol(""),Bn=Symbol("");let Ln;re({[wn]:"vModelRadio",[_n]:"vModelCheckbox",[Cn]:"vModelText",[Mn]:"vModelSelect",[On]:"vModelDynamic",[In]:"withModifiers",[Pn]:"withKeys",[Vn]:"vShow",[Fn]:"Transition",[Bn]:"TransitionGroup"});const En=e("style,iframe,script,noscript",!0),An={isVoidTag:c,isNativeTag:e=>r(e)||s(e),isPreTag:e=>"pre"===e,decodeEntities:function(e){return(Ln||(Ln=document.createElement("div"))).innerHTML=e,Ln.textContent},isBuiltInComponent:e=>$e(e,"Transition")?Fn:$e(e,"TransitionGroup")?Bn:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(En(e))return 2}return 0}},jn=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:ae("style",!0,t.loc),exp:Hn(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},Hn=(e,t)=>{const r=function(e){const t={};return e.split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return ae(JSON.stringify(r),!1,t,3)};function Rn(e,t){return $(e,t)}const Dn=e("passive,once,capture"),Un=e("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),zn=e("left,right"),Jn=e("onkeyup,onkeydown,onkeypress",!0),Gn=(e,t)=>Te(e)&&"onclick"===e.content.toLowerCase()?ae(t,!0):4!==e.type?de(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,qn=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Kn=[jn],Wn={cloak:$n,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ue(ae("innerHTML",!0,r),o||ae("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[ue(ae("textContent",!0),o?he(n.helperString(z),[o],r):ae("",!0))]}},model:(e,t,n)=>{const o=Sn(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=Cn,c=!1;if("input"===r||s){const n=Le(t,"type");if(n){if(7===n.type)e=On;else if(n.value)switch(n.value.content){case"radio":e=wn;break;case"checkbox":e=_n;break;case"file":c=!0}}else Ae(t)&&(e=On)}else"select"===r&&(e=Mn);c||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>gn(e,0,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:c,nonKeyModifiers:i,eventOptionModifiers:l}=((e,t)=>{const n=[],o=[],r=[];for(let s=0;s<t.length;s++){const c=t[s];Dn(c)?r.push(c):zn(c)?Te(e)?Jn(e.content)?n.push(c):o.push(c):(n.push(c),o.push(c)):Un(c)?o.push(c):n.push(c)}return{keyModifiers:n,nonKeyModifiers:o,eventOptionModifiers:r}})(r,o);if(i.includes("right")&&(r=Gn(r,"onContextmenu")),i.includes("middle")&&(r=Gn(r,"onMouseup")),i.length&&(s=he(n.helper(In),[s,JSON.stringify(i)])),!c.length||Te(r)&&!Jn(r.content)||(s=he(n.helper(Pn),[s,JSON.stringify(c)])),l.length){const e=l.map(k).join("");r=Te(r)?ae(`${r.content}${e}`,!0):de(["(",r,`) + "${e}"`])}return{props:[ue(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(Vn)})};function Zn(e,t={}){return Tn(e,a({},An,t,{nodeTransforms:[qn,...Kn,...t.nodeTransforms||[]],directiveTransforms:a({},Wn,t.directiveTransforms||{}),transformHoist:null}))}function Qn(e,t={}){return We(e,a({},An,t))}export{O as BASE_TRANSITION,q as CAMELIZE,K as CAPITALIZE,P as CREATE_BLOCK,F as CREATE_COMMENT,U as CREATE_SLOTS,L as CREATE_STATIC,B as CREATE_TEXT,V as CREATE_VNODE,Wn as DOMDirectiveTransforms,Kn as DOMNodeTransforms,w as FRAGMENT,ne as IS_REF,M as KEEP_ALIVE,J as MERGE_PROPS,I as OPEN_BLOCK,Y as POP_SCOPE_ID,Q as PUSH_SCOPE_ID,R as RENDER_LIST,D as RENDER_SLOT,E as RESOLVE_COMPONENT,j as RESOLVE_DIRECTIVE,A as RESOLVE_DYNAMIC_COMPONENT,Z as SET_BLOCK_TRACKING,C as SUSPENSE,_ as TELEPORT,z as TO_DISPLAY_STRING,G as TO_HANDLERS,W as TO_HANDLER_KEY,Fn as TRANSITION,Bn as TRANSITION_GROUP,te as UNREF,_n as V_MODEL_CHECKBOX,On as V_MODEL_DYNAMIC,wn as V_MODEL_RADIO,Mn as V_MODEL_SELECT,Cn as V_MODEL_TEXT,Pn as V_ON_WITH_KEYS,In as V_ON_WITH_MODIFIERS,Vn as V_SHOW,ee as WITH_CTX,H as WITH_DIRECTIVES,X as WITH_SCOPE_ID,Pe as advancePositionWithClone,Ve as advancePositionWithMutation,Fe as assert,Tn as baseCompile,We as baseParse,pn as buildProps,nn as buildSlots,Zn as compile,le as createArrayExpression,Se as createAssignmentExpression,ve as createBlockStatement,ye as createCacheExpression,he as createCallExpression,$ as createCompilerError,de as createCompoundExpression,ge as createConditionalExpression,Rn as createDOMCompilerError,Qt as createForLoopParams,me as createFunctionExpression,xe as createIfStatement,fe as createInterpolation,pe as createObjectExpression,ue as createObjectProperty,Ne as createReturnStatement,ce as createRoot,ke as createSequenceExpression,ae as createSimpleExpression,Ct as createStructuralDirectiveTransform,be as createTemplateLiteral,$t as createTransformContext,ie as createVNodeCall,Be as findDir,Le as findProp,Mt as generate,t as generateCodeFrame,Nn as getBaseTransformPreset,Ie as getInnerRange,Ae as hasDynamicKeyVBind,Je as hasScopeRef,oe as helperNameMap,Ue as injectProp,Ee as isBindKey,$e as isBuiltInType,we as isCoreComponent,Oe as isMemberExpression,Ce as isSimpleIdentifier,De as isSlotOutlet,Te as isStaticExp,Re as isTemplateNode,je as isText,He as isVSlot,se as locStub,$n as noopDirectiveTransform,Qn as parse,An as parserOptions,At as processExpression,Jt as processFor,Ht as processIf,hn as processSlotOutlet,re as registerRuntimeHelpers,ln as resolveComponentType,ze as toValidAssetId,Xt as trackSlotScopes,en as trackVForSlotScopes,wt as transform,yn as transformBind,cn as transformElement,Et as transformExpression,Sn as transformModel,gn as transformOn,jn as transformStyle,_t as traverseNode};
