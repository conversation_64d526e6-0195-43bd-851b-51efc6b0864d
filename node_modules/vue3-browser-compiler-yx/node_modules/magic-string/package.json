{"name": "magic-string", "description": "Modify strings, generate sourcemaps", "author": "<PERSON>", "version": "0.25.7", "repository": "https://github.com/rich-harris/magic-string", "main": "dist/magic-string.cjs.js", "module": "dist/magic-string.es.js", "jsnext:main": "dist/magic-string.es.js", "typings": "index.d.ts", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.4"}, "devDependencies": {"eslint": "^5.16.0", "mocha": "^5.2.0", "prettier": "^1.18.2", "rollup": "^1.16.3", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "source-map": "^0.6.1", "source-map-support": "^0.5.12"}, "keywords": ["string", "string manipulation", "sourcemap", "templating", "transpilation"], "scripts": {"test": "mocha", "pretest": "npm run lint && npm run build", "format": "prettier --single-quote --print-width 100 --use-tabs --write src/*.js src/**/*.js", "build": "rollup -c", "prepare": "npm run build", "prepublishOnly": "rm -rf dist && npm test", "lint": "eslint src test", "watch": "rollup -cw"}, "files": ["dist/*", "index.d.ts", "README.md"]}