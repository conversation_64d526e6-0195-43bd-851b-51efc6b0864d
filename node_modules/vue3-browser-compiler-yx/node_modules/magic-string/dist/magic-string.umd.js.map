{"version": 3, "file": "magic-string.umd.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../node_modules/sourcemap-codec/dist/sourcemap-codec.es.js", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js", "../src/index-legacy.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\t// we make these non-enumerable, for sanity while debugging\n\t\tObject.defineProperties(this, {\n\t\t\tprevious: { writable: true, value: null },\n\t\t\tnext:     { writable: true, value: null }\n\t\t});\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.end - trimmed.length);\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "var charToInteger = {};\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nfor (var i = 0; i < chars.length; i++) {\n    charToInteger[chars.charCodeAt(i)] = i;\n}\nfunction decode(mappings) {\n    var generatedCodeColumn = 0; // first field\n    var sourceFileIndex = 0; // second field\n    var sourceCodeLine = 0; // third field\n    var sourceCodeColumn = 0; // fourth field\n    var nameIndex = 0; // fifth field\n    var decoded = [];\n    var line = [];\n    var segment = [];\n    for (var i = 0, j = 0, shift = 0, value = 0, len = mappings.length; i < len; i++) {\n        var c = mappings.charCodeAt(i);\n        if (c === 44) { // \",\"\n            if (segment.length)\n                line.push(new Int32Array(segment));\n            segment = [];\n            j = 0;\n        }\n        else if (c === 59) { // \";\"\n            if (segment.length)\n                line.push(new Int32Array(segment));\n            segment = [];\n            j = 0;\n            decoded.push(line);\n            line = [];\n            generatedCodeColumn = 0;\n        }\n        else {\n            var integer = charToInteger[c];\n            if (integer === undefined) {\n                throw new Error('Invalid character (' + String.fromCharCode(c) + ')');\n            }\n            var hasContinuationBit = integer & 32;\n            integer &= 31;\n            value += integer << shift;\n            if (hasContinuationBit) {\n                shift += 5;\n            }\n            else {\n                var shouldNegate = value & 1;\n                value >>= 1;\n                var num = shouldNegate ? -value : value;\n                if (j == 0) {\n                    generatedCodeColumn += num;\n                    segment.push(generatedCodeColumn);\n                }\n                else if (j === 1) {\n                    sourceFileIndex += num;\n                    segment.push(sourceFileIndex);\n                }\n                else if (j === 2) {\n                    sourceCodeLine += num;\n                    segment.push(sourceCodeLine);\n                }\n                else if (j === 3) {\n                    sourceCodeColumn += num;\n                    segment.push(sourceCodeColumn);\n                }\n                else if (j === 4) {\n                    nameIndex += num;\n                    segment.push(nameIndex);\n                }\n                j++;\n                value = shift = 0; // reset\n            }\n        }\n    }\n    if (segment.length)\n        line.push(new Int32Array(segment));\n    decoded.push(line);\n    return decoded;\n}\nfunction encode(decoded) {\n    var sourceFileIndex = 0; // second field\n    var sourceCodeLine = 0; // third field\n    var sourceCodeColumn = 0; // fourth field\n    var nameIndex = 0; // fifth field\n    var mappings = '';\n    for (var i = 0; i < decoded.length; i++) {\n        var line = decoded[i];\n        if (i > 0)\n            mappings += ';';\n        if (line.length === 0)\n            continue;\n        var generatedCodeColumn = 0; // first field\n        var lineMappings = [];\n        for (var _i = 0, line_1 = line; _i < line_1.length; _i++) {\n            var segment = line_1[_i];\n            var segmentMappings = encodeInteger(segment[0] - generatedCodeColumn);\n            generatedCodeColumn = segment[0];\n            if (segment.length > 1) {\n                segmentMappings +=\n                    encodeInteger(segment[1] - sourceFileIndex) +\n                        encodeInteger(segment[2] - sourceCodeLine) +\n                        encodeInteger(segment[3] - sourceCodeColumn);\n                sourceFileIndex = segment[1];\n                sourceCodeLine = segment[2];\n                sourceCodeColumn = segment[3];\n            }\n            if (segment.length === 5) {\n                segmentMappings += encodeInteger(segment[4] - nameIndex);\n                nameIndex = segment[4];\n            }\n            lineMappings.push(segmentMappings);\n        }\n        mappings += lineMappings.join(',');\n    }\n    return mappings;\n}\nfunction encodeInteger(num) {\n    var result = '';\n    num = num < 0 ? (-num << 1) | 1 : num << 1;\n    do {\n        var clamped = num & 31;\n        num >>= 5;\n        if (num > 0) {\n            clamped |= 32;\n        }\n        result += chars[clamped];\n    } while (num > 0);\n    return result;\n}\n\nexport { decode, encode };\n//# sourceMappingURL=sourcemap-codec.es.js.map\n", "import { encode } from 'sourcemap-codec';\n\nlet btoa = () => {\n\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n};\nif (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n\tbtoa = str => window.btoa(unescape(encodeURIComponent(str)));\n} else if (typeof Buffer === 'function') {\n\tbtoa = str => Buffer.from(str, 'utf-8').toString('base64');\n}\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter(line => /^\\t+/.test(line));\n\tconst spaced = lines.filter(line => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "export default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t}\n\n\t\tthis.advance(content);\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\tthis.rawSegments.push([this.generatedCodeColumn, sourceIndex, loc.line, loc.column]);\n\t\t\t}\n\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t} else {\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal:              { writable: true, value: string },\n\t\t\toutro:                 { writable: true, value: '' },\n\t\t\tintro:                 { writable: true, value: '' },\n\t\t\tfirstChunk:            { writable: true, value: chunk },\n\t\t\tlastChunk:             { writable: true, value: chunk },\n\t\t\tlastSearchedChunk:     { writable: true, value: chunk },\n\t\t\tbyStart:               { writable: true, value: {} },\n\t\t\tbyEnd:                 { writable: true, value: {} },\n\t\t\tfilename:              { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations:    { writable: true, value: new BitSet() },\n\t\t\tstoredNames:           { writable: true, value: {} },\n\t\t\tindentStr:             { writable: true, value: guessIndent(string) }\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext(chunk => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: [options.source ? getRelativePath(options.file || '', options.source) : null],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : [null],\n\t\t\tnames,\n\t\t\tmappings: mappings.raw\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tindentStr = indentStr !== undefined ? indentStr : this.indentStr || '\\t';\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach(exclusion => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = match => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error('magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)');\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn('magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead'); // eslint-disable-line no-console\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn('magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead'); // eslint-disable-line no-console\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error('Cannot overwrite a zero-length range – use appendLeft or prependRight instead');\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn('The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string'); // eslint-disable-line no-console\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst contentOnly = options !== undefined ? options.contentOnly : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tthis.storedNames[original] = true;\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tif (end > first.end && first.next !== this.byStart[first.end]) {\n\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, contentOnly);\n\n\t\t\tif (first !== last) {\n\t\t\t\tlet chunk = first.next;\n\t\t\t\twhile (chunk !== last) {\n\t\t\t\t\tchunk.edit('', false);\n\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length)\n\t\t\treturn this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length)\n\t\t\t\treturn chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length)\n\t\t\t\treturn chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length)\n\t\t\t\treturn chunk.intro[chunk.intro.length - 1];\n\t\t} while (chunk = chunk.previous);\n\t\tif (this.intro.length)\n\t\t\treturn this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1)\n\t\t\treturn this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while (chunk = chunk.previous);\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1)\n\t\t\treturn this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${\n\t\t\t\t\tchunk.original\n\t\t\t\t}\")`\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (chunk.intro.length && chunk.intro.trim() ||\n\t\t\t\t\tchunk.content.length && chunk.content.trim() ||\n\t\t\t\t\tchunk.outro.length && chunk.outro.trim())\n\t\t\t\treturn false;\n\t\t} while (chunk = chunk.next);\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while (chunk = chunk.next);\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error('bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`');\n\t\t}\n\n\t\t['filename', 'indentExclusionRanges', 'separator'].forEach(option => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || ''\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator\n\t\t});\n\n\t\tthis.sources.forEach(source => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tthis.sources.forEach(source => {\n\t\t\tObject.keys(source.content.storedNames).forEach(name => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext(chunk => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: this.uniqueSources.map(source => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map(source => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach(source => {\n\t\t\tconst indentStr = source.content.indentStr;\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty () {\n\t\tif (this.intro.length && this.intro.trim())\n\t\t\treturn false;\n\t\tif (this.sources.some(source => !source.content.isEmpty()))\n\t\t\treturn false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce((length, source) => length + source.content.length(), this.intro.length);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport Bundle from './Bundle.js';\nimport SourceMap from './SourceMap.js';\n\nMagicString.Bundle = Bundle;\nMagicString.SourceMap = SourceMap;\nMagicString.default = MagicString; // work around TypeScript bug https://github.com/Rich-Harris/magic-string/pull/121\n\nexport default MagicString;\n"], "names": ["const", "let", "this"], "mappings": ";;;;;;CAAe,IAAM,MAAM,GAC1B,eAAW,CAAC,GAAG,EAAE;EAChB,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;CAC3D,EAAC;;CAEF,iBAAC,oBAAI,CAAC,EAAE;EACN,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;CACpC,EAAC;;CAEF,iBAAC,oBAAI,CAAC,EAAE;EACP,OAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;CAChD,CAAC;;CCXa,IAAM,KAAK,GACzB,cAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;EAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;EACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;;EAExB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;EAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;EAEhB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;EACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;;;EAGpB,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;GAC9B,QAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;GAC1C,IAAK,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;GACzC,CAAC,CAAC;CACJ,EAAC;;CAEF,gBAAC,kCAAW,OAAO,EAAE;EACnB,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACvB,EAAC;;CAEF,gBAAC,oCAAY,OAAO,EAAE;EACrB,IAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;CACnC,EAAC;;CAEF,gBAAC,0BAAQ;EACPA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;EAE7D,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACzB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACzB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EAC7B,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EACjC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;;EAE5B,OAAQ,KAAK,CAAC;CACd,EAAC;;CAEF,gBAAC,8BAAS,KAAK,EAAE;EACf,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;CAC/C,EAAC;;CAEF,gBAAC,8BAAS,EAAE,EAAE;EACZC,IAAI,KAAK,GAAG,IAAI,CAAC;EAClB,OAAQ,KAAK,EAAE;GACb,EAAE,CAAC,KAAK,CAAC,CAAC;GACV,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB;CACF,EAAC;;CAEF,gBAAC,sCAAa,EAAE,EAAE;EAChBA,IAAI,KAAK,GAAG,IAAI,CAAC;EAClB,OAAQ,KAAK,EAAE;GACb,EAAE,CAAC,KAAK,CAAC,CAAC;GACV,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;GACvB;CACF,EAAC;;CAEF,gBAAC,sBAAK,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;EACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EACxB,IAAK,CAAC,WAAW,EAAE;GACjB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;GAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;GAChB;EACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;EAE3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;;EAEpB,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,gBAAC,oCAAY,OAAO,EAAE;EACrB,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAC;;CAEF,gBAAC,sCAAa,OAAO,EAAE;EACtB,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,EAAC;;CAEF,gBAAC,wBAAM,KAAK,EAAE;EACb,IAAO,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;EAEtCD,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;EAC1DA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;;EAEtD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;;EAE/BA,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;EAC3D,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EAC5B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;EAEhB,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;;EAEjB,IAAI,IAAI,CAAC,MAAM,EAAE;;GAEjB,QAAS,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;GACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;GAClB,MAAM;GACN,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;GAC9B;;EAED,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EAC1B,IAAI,QAAQ,CAAC,IAAI,IAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAC;EACrD,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;EACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;;EAEtB,OAAQ,QAAQ,CAAC;CACjB,EAAC;;CAEF,gBAAC,gCAAW;EACV,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CAC/C,EAAC;;CAEF,gBAAC,4BAAQ,EAAE,EAAE;EACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;EAEnCA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAE7C,IAAI,OAAO,CAAC,MAAM,EAAE;GACnB,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;IAC9B,IAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAClE;GACF,OAAQ,IAAI,CAAC;;GAEZ,MAAM;GACP,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;GAE/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;GACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;GACnC;CACF,EAAC;;CAEF,gBAAC,gCAAU,EAAE,EAAE;EACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;EAEnCA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAE7C,IAAI,OAAO,CAAC,MAAM,EAAE;GACnB,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;IAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACvC,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC/B;GACF,OAAQ,IAAI,CAAC;;GAEZ,MAAM;GACP,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;GAE/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;GACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;GACnC;CACF,CAAC;;CCzJF,IAAI,KAAK,GAAG,mEAAmE,CAAC;AAChF,CA0EA,SAAS,MAAM,CAAC,OAAO,EAAE;CACzB,IAAI,IAAI,eAAe,GAAG,CAAC,CAAC;CAC5B,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC;CAC3B,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC;CAC7B,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC;CACtB,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC;CACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC7C,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC9B,QAAQ,IAAI,CAAC,GAAG,CAAC;CACjB,YAAY,QAAQ,IAAI,GAAG,CAAC;CAC5B,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;CAC7B,YAAY,SAAS;CACrB,QAAQ,IAAI,mBAAmB,GAAG,CAAC,CAAC;CACpC,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC;CAC9B,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;CAClE,YAAY,IAAI,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;CACrC,YAAY,IAAI,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC;CAClF,YAAY,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC7C,YAAY,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;CACpC,gBAAgB,eAAe;CAC/B,oBAAoB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;CAC/D,wBAAwB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;CAClE,wBAAwB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC;CACrE,gBAAgB,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC7C,gBAAgB,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC5C,gBAAgB,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAC9C,aAAa;CACb,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;CACtC,gBAAgB,eAAe,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;CACzE,gBAAgB,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACvC,aAAa;CACb,YAAY,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;CAC/C,SAAS;CACT,QAAQ,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAC3C,KAAK;CACL,IAAI,OAAO,QAAQ,CAAC;CACpB,CAAC;CACD,SAAS,aAAa,CAAC,GAAG,EAAE;CAC5B,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;CACpB,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;CAC/C,IAAI,GAAG;CACP,QAAQ,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,CAAC;CAC/B,QAAQ,GAAG,KAAK,CAAC,CAAC;CAClB,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;CACrB,YAAY,OAAO,IAAI,EAAE,CAAC;CAC1B,SAAS;CACT,QAAQ,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;CACjC,KAAK,QAAQ,GAAG,GAAG,CAAC,EAAE;CACtB,IAAI,OAAO,MAAM,CAAC;CAClB,CAAC;;CC3HDC,IAAI,IAAI,eAAM;EACb,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;EAC3F,CAAC;CACF,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;EACvE,IAAI,aAAG,KAAI,SAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAC,CAAC;EAC7D,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;EACxC,IAAI,aAAG,KAAI,SAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAC,CAAC;EAC3D;;CAEc,IAAM,SAAS,GAC7B,kBAAW,CAAC,UAAU,EAAE;EACvB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;EACjB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;EAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;EAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;EAChD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;EAC/B,IAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;CAC7C,EAAC;;CAEF,oBAAC,gCAAW;EACV,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CAC7B,EAAC;;CAEF,oBAAC,0BAAQ;EACR,OAAQ,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC9E,CAAC;;CC3Ba,SAAS,WAAW,CAAC,IAAI,EAAE;EACzCD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;EAE/BA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAC,MAAK,SAAG,MAAM,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;EACvDA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAC,MAAK,SAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;;EAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;GAC/C,OAAO,IAAI,CAAC;GACZ;;;;;EAKD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;GACnC,OAAO,IAAI,CAAC;GACZ;;;EAGDA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,WAAE,QAAQ,EAAE,OAAO,EAAE;GAC7CA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;GAChD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;GACrC,EAAE,QAAQ,CAAC,CAAC;;EAEb,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACpC;;CCxBc,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;EACjDA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;EACtCA,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;EAElC,SAAS,CAAC,GAAG,EAAE,CAAC;;EAEhB,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;GACnC,SAAS,CAAC,KAAK,EAAE,CAAC;GAClB,OAAO,CAAC,KAAK,EAAE,CAAC;GAChB;;EAED,IAAI,SAAS,CAAC,MAAM,EAAE;GACrBC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;GACzB,OAAO,CAAC,EAAE,IAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAC;GAChC;;EAED,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC3C;;CCjBDD,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;;AAE3C,CAAe,SAAS,QAAQ,CAAC,KAAK,EAAE;EACvC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;EAClD;;CCJc,SAAS,UAAU,CAAC,MAAM,EAAE;EAC1CA,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EACzCA,IAAM,WAAW,GAAG,EAAE,CAAC;;EAEvB,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;GACvD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;GACtB,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;GACnC;;EAED,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;GAC7BA,IAAI,CAAC,GAAG,CAAC,CAAC;GACVA,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;GAC3B,OAAO,CAAC,GAAG,CAAC,EAAE;IACbD,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvB,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;KAC3B,CAAC,GAAG,CAAC,CAAC;KACN,MAAM;KACN,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACV;IACD;GACDA,IAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;GACnBA,IAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;GACzC,OAAO,QAAE,IAAI,UAAE,MAAM,EAAE,CAAC;GACxB,CAAC;EACF;;CCxBc,IAAM,QAAQ,GAC5B,iBAAW,CAAC,KAAK,EAAE;EAClB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;EACnB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;EAC3B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;EAC7B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;EACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;EACzD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;;CAEF,mBAAC,4BAAQ,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;EAC7C,IAAI,OAAO,CAAC,MAAM,EAAE;GACnBA,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;GAC9E,IAAI,SAAS,IAAI,CAAC,EAAE;IACnB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxB;GACF,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;GAC/B,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;GACzB,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;GACpC;;EAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;EACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;;CAEF,mBAAC,8CAAiB,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;EACvEC,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;EACpCA,IAAI,KAAK,GAAG,IAAI,CAAC;;EAEjB,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;GACrC,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;IACtE,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACrF;;GAED,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;IACzC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;IACd,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACzD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;IAC9B,KAAM,GAAG,IAAI,CAAC;IACb,MAAM;IACN,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IAChB,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC/B,KAAM,GAAG,KAAK,CAAC;IACd;;GAEF,iBAAkB,IAAI,CAAC,CAAC;GACvB;;EAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACrB,EAAC;;CAEF,mBAAC,4BAAQ,GAAG,EAAE;EACZ,IAAI,CAAC,GAAG,IAAE,SAAO;;EAElB,IAAO,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;EAE9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;GACrB,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC1C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IACzD;GACD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;GAC7B;;EAED,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;CAC5D,CAAC;;CCzDFD,IAAM,CAAC,GAAG,IAAI,CAAC;;CAEfA,IAAM,MAAM,GAAG;EACd,UAAU,EAAE,KAAK;EACjB,WAAW,EAAE,KAAK;EAClB,SAAS,EAAE,KAAK;EAChB,CAAC;;CAEF,IAAqB,WAAW,GAC/B,oBAAW,CAAC,MAAM,EAAE,OAAY,EAAE;mCAAP,GAAG;;EAC7BA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;;EAElD,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;GAC9B,QAAS,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;GACzD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;GACrD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;GACrD,UAAW,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;GACxD,SAAU,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;GACxD,iBAAkB,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;GACxD,OAAQ,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;GACrD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;GACpD,QAAQ,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;GAClE,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;GAC/E,kBAAkB,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;GAC/D,WAAY,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;GACpD,SAAS,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;GACrE,CAAC,CAAC;;EAMJ,IAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACzB,IAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;CACnC,EAAC;;CAEF,sBAAC,sDAAqB,IAAI,EAAE;EAC3B,IAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACnC,EAAC;;CAEF,sBAAC,0BAAO,OAAO,EAAE;EACf,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;;EAEvF,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;EACvB,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,kCAAW,KAAK,EAAE,OAAO,EAAE;EAC1B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;EAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;EAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;EAEjC,IAAK,KAAK,EAAE;GACV,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;GAC1B,MAAM;GACN,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;GACtB;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;EAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;EAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;EAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;EAEnC,IAAK,KAAK,EAAE;GACV,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;GAC3B,MAAM;GACN,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;GACtB;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,0BAAQ;EACPA,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;;EAE3EC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;EACpCA,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;;EAE1F,OAAQ,aAAa,EAAE;GACtB,MAAO,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;GACjD,MAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;;GAE5CD,IAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC;GAC9C,IAAO,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAC;;GAExE,IAAK,eAAe,EAAE;IACpB,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC;IACnC,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAC;;IAExC,WAAY,GAAG,eAAe,CAAC;IAC9B;;GAEF,aAAc,GAAG,iBAAiB,CAAC;GAClC;;EAED,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;;EAE/B,IAAI,IAAI,CAAC,qBAAqB,EAAE;GAChC,MAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;GAClE;;EAEF,MAAO,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;EAEhE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;EAE3B,OAAQ,MAAM,CAAC;CACf,EAAC;;CAEF,sBAAC,kDAAmB,OAAO,EAAE;;;EAC3B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;;EAExBA,IAAM,WAAW,GAAG,CAAC,CAAC;EACtBA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EAC7C,IAAO,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;EAE9C,IAAO,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;EAEzC,IAAI,IAAI,CAAC,KAAK,EAAE;GAChB,QAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;GAC7B;;EAEF,IAAK,CAAC,UAAU,CAAC,QAAQ,WAAC,OAAM;GAC/B,IAAO,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;GAEhC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;;GAEtD,IAAI,KAAK,CAAC,MAAM,EAAE;IAClB,QAAS,CAAC,OAAO;KACf,WAAW;KACZ,KAAM,CAAC,OAAO;KACb,GAAG;KACH,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACpD,CAAC;IACF,MAAM;IACN,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAEE,MAAI,CAAC,QAAQ,EAAE,GAAG,EAAEA,MAAI,CAAC,kBAAkB,CAAC,CAAC;IAC3F;;GAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;GACtD,CAAC,CAAC;;EAEH,OAAO;GACN,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;GAC9D,OAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;GACtF,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;GAClE,OAAC,KAAK;GACL,QAAQ,EAAE,QAAQ,CAAC,GAAG;GACtB,CAAC;CACH,EAAC;;CAEF,sBAAC,oCAAY,OAAO,EAAE;EACrB,OAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;CACxD,EAAC;;CAEF,sBAAC,8CAAkB;EACjB,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;CACxD,EAAC;;CAEF,sBAAC,0BAAO,SAAS,EAAE,OAAO,EAAE;EAC1BF,IAAM,OAAO,GAAG,YAAY,CAAC;;EAE7B,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;GACzB,OAAQ,GAAG,SAAS,CAAC;GACrB,SAAU,GAAG,SAAS,CAAC;GACtB;;EAED,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;;EAEzE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;;EAElC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;;;EAGxBA,IAAM,UAAU,GAAG,EAAE,CAAC;;EAEtB,IAAI,OAAO,CAAC,OAAO,EAAE;GACrB,IAAO,UAAU;IACf,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;GAC9E,UAAU,CAAC,OAAO,WAAC,WAAU;IAC7B,KAAMC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;KACpD,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACrB;IACD,CAAC,CAAC;GACH;;EAEF,IAAK,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;EAC9DD,IAAM,QAAQ,aAAG,OAAM;GACvB,IAAK,yBAAyB,IAAE,aAAU,SAAS,GAAG,KAAK,IAAG;GAC9D,yBAA0B,GAAG,IAAI,CAAC;GAClC,OAAQ,KAAK,CAAC;GACb,CAAC;;EAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;EAEnDC,IAAI,SAAS,GAAG,CAAC,CAAC;EAClBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;;EAE7B,OAAQ,KAAK,EAAE;GACbD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;;GAEtB,IAAI,KAAK,CAAC,MAAM,EAAE;IACjB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;KAC3B,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;KAEzD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;MACzB,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC;MAC7E;KACD;IACD,MAAM;IACN,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;;IAExB,OAAO,SAAS,GAAG,GAAG,EAAE;KACvB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;MAC5B,IAAO,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;;MAEtC,IAAI,IAAI,KAAK,IAAI,EAAE;OACnB,yBAA0B,GAAG,IAAI,CAAC;OACjC,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;OACvD,yBAA0B,GAAG,KAAK,CAAC;;OAElC,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;QAC9B,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC9B,MAAM;QACP,IAAK,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACnC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACnB,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC9B;OACD;MACD;;KAEF,SAAU,IAAI,CAAC,CAAC;KACf;IACD;;GAED,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC;GACtB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB;;EAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;EAEpD,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,4BAAS;EACR,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;CACpG,EAAC;;CAEF,sBAAC,kCAAW,KAAK,EAAE,OAAO,EAAE;EAC1B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;GACvB,OAAO,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;GACnG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;GACzB;;EAEF,OAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CACxC,EAAC;;CAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;EAC3B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;GACxB,OAAO,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;GACtG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;GAC1B;;EAEF,OAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC1C,EAAC;;CAEF,sBAAC,sBAAK,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EACvB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,GAAC;;EAI7F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;EACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;EAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EACnC,IAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;EAE7BA,IAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;EAC/BA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;;EAE5B,IAAO,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EACrC,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,IAAE,OAAO,IAAI,GAAC;EACtDA,IAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;EAE/D,IAAK,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,GAAC;EACtC,IAAK,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,GAAC;;EAE3C,IAAK,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,KAAK,GAAC;EACnC,IAAK,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAC;;EAEvC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAC;EACjD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;GACf,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;GAChC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;GAC3B;;EAED,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;EACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;;EAE9B,IAAK,CAAC,OAAO,IAAE,IAAI,CAAC,UAAU,GAAG,KAAK,GAAC;EACvC,IAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAC;EAGtC,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,gCAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;EACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,GAAC;;EAE7F,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;EAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;EAE5C,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,GAAC;EACzE,IAAK,KAAK,KAAK,GAAG;GACjB,EAAC,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,GAAC;;EAIlG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;;EAEjB,IAAI,OAAO,KAAK,IAAI,EAAE;GACrB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;IACtB,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;IAC9I,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB;;GAED,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;GAC9B;EACDA,IAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;EACpEA,IAAM,WAAW,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;;EAEzE,IAAK,SAAS,EAAE;GACdA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;GAClD,IAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;GAClC;;EAEF,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EACnC,IAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;EAE9B,IAAK,KAAK,EAAE;GACV,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;IAC9D,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IACzD;;GAEF,KAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;;GAE5C,IAAI,KAAK,KAAK,IAAI,EAAE;IACnBC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;IACvB,OAAO,KAAK,KAAK,IAAI,EAAE;KACvB,KAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;KACtB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;KACnB;;IAEF,KAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACtB;GACD,MAAM;;GAEP,IAAO,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;;;GAGpE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;GACrB,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;GACzB;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,4BAAQ,OAAO,EAAE;EAChB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;;EAExF,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EACnC,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;EAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;EAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;EAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;EAEjC,IAAK,KAAK,EAAE;GACV,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;GAC3B,MAAM;GACP,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;GAClC;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,sCAAa,KAAK,EAAE,OAAO,EAAE;EAC5B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;EAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;EAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;EAEnC,IAAK,KAAK,EAAE;GACV,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;GAC5B,MAAM;GACP,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;GAClC;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,0BAAO,KAAK,EAAE,GAAG,EAAE;EAClB,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;EAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;EAE5C,IAAI,KAAK,KAAK,GAAG,IAAE,OAAO,IAAI,GAAC;;EAE/B,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,GAAC;EAC5F,IAAK,KAAK,GAAG,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,GAAC;;EAInE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;;EAElB,IAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;EAEjC,OAAQ,KAAK,EAAE;GACb,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;GACjB,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;GACjB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;GAEf,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;GACzD;EAGF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,gCAAW;EACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;GACrB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;EAC1CA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;EAC3B,GAAG;GACF,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM;IACtB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;GAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM;IACxB,EAAC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;GAChD,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM;IACtB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;GAC5C,QAAQ,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE;EACjC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;GACrB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;EAC3C,OAAQ,EAAE,CAAC;CACX,EAAC;;CAEF,sBAAC,gCAAW;EACVA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EAC1C,IAAI,SAAS,KAAK,CAAC,CAAC;GACpB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAC;EACzCA,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EACzBA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;EAC3B,GAAG;GACH,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;IAC5B,SAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,SAAS,KAAK,CAAC,CAAC;KACpB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;IACpD,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;IAChC;;GAEF,IAAK,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;IAC9B,SAAU,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI,SAAS,KAAK,CAAC,CAAC;KACpB,EAAC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;IACtD,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC;;GAEF,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;IAC5B,SAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,SAAS,KAAK,CAAC,CAAC;KACpB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;IACpD,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;IAChC;GACD,QAAQ,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE;EAClC,SAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;EACtC,IAAI,SAAS,KAAK,CAAC,CAAC;GACpB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;EACnD,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;CAC7B,EAAC;;CAEF,sBAAC,wBAAM,KAAS,EAAE,GAA0B,EAAE;gCAAlC,GAAG;4BAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;EACpC,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;EAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;EAE5CA,IAAI,MAAM,GAAG,EAAE,CAAC;;;EAGhBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;EAC5B,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;;GAE5D,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;IAC3C,OAAQ,MAAM,CAAC;IACd;;GAED,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB;;EAEF,IAAK,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;GAClD,EAAC,MAAM,IAAI,KAAK,qCAAkC,KAAK,8BAA0B,GAAC;;EAElFD,IAAM,UAAU,GAAG,KAAK,CAAC;EAC1B,OAAQ,KAAK,EAAE;GACb,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;IACnE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;IACtB;;GAEDA,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;GAC3D,IAAK,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;IACpD,EAAC,MAAM,IAAI,KAAK,qCAAkC,GAAG,4BAAwB,GAAC;;GAE9EA,IAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;GACnE,IAAO,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;;GAE7F,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;;GAEpD,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;IACvD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;IACtB;;GAEF,IAAK,WAAW,EAAE;IAChB,MAAM;IACN;;GAED,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB;;EAEF,OAAQ,MAAM,CAAC;CACf,EAAC;;CAEF;CACA,sBAAC,sBAAK,KAAK,EAAE,GAAG,EAAE;EACjB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;EAC5B,KAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EACvB,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;;EAE1C,OAAQ,KAAK,CAAC;CACd,EAAC;;CAEF,sBAAC,0BAAO,KAAK,EAAE;EACb,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAE,SAAO;;EAIrDC,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;EACpC,IAAO,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;;EAEzC,OAAQ,KAAK,EAAE;GACb,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAC;;GAElE,KAAM,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;GAC1E;CACF,EAAC;;CAEF,sBAAC,oCAAY,KAAK,EAAE,KAAK,EAAE;EAC1B,IAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;;GAEzCD,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;GAC9C,MAAO,IAAI,KAAK;8DACwC,GAAG,CAAC,KAAI,UAAI,GAAG,CAAC,OAAM,cAC3E,KAAK,CAAC,SAAQ;IAEf,CAAC;GACF;;EAEF,IAAO,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;EAErC,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAC3B,IAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;EAChC,IAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;;EAEpC,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAC;;EAExD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;EAEhC,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,gCAAW;EACVC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;;EAErBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;EAC7B,OAAQ,KAAK,EAAE;GACb,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;GACxB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB;;EAED,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;CACzB,EAAC;;CAEF,sBAAC,8BAAU;EACTA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;EAC5B,GAAG;GACF,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;KAC3C,KAAM,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;KAC7C,KAAM,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;IAC1C,EAAC,OAAO,KAAK,GAAC;GACd,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;EAC9B,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,sBAAC,4BAAS;EACRA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;EAC5BA,IAAI,MAAM,GAAG,CAAC,CAAC;EACf,GAAG;GACF,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;GACzE,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;EAC9B,OAAQ,MAAM,CAAC;CACf,EAAC;;CAEF,sBAAC,kCAAY;EACX,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC9B,EAAC;;CAEF,sBAAC,sBAAK,QAAQ,EAAE;EACd,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CACnD,EAAC;;CAEF,sBAAC,0CAAe,QAAQ,EAAE;EACxBD,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;;EAElD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;EAEnCC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;;EAE3B,GAAG;GACFD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;GACvB,IAAO,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;;GAGlC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;IACtB,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;KAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;KAC5B;;IAEF,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;IACxC;;GAED,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;GACzB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;GACvB,QAAQ,KAAK,EAAE;;EAEjB,OAAQ,KAAK,CAAC;CACd,EAAC;;CAEF,sBAAC,4BAAQ,QAAQ,EAAE;EACjB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;EAC/B,OAAQ,IAAI,CAAC;CACb,EAAC;CACF,sBAAC,8CAAiB,QAAQ,EAAE;EAC1BA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;;EAEvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;EAEnCC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;;EAE5B,GAAG;GACFD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;GACvB,IAAO,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;;GAEpC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;;IAEtB,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,GAAC;;IAE3D,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;IACxC;;GAED,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;GACzB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACnB,QAAQ,KAAK,EAAE;;EAEjB,OAAQ,KAAK,CAAC;CACd,EAAC;;CAEF,sBAAC,gCAAU,QAAQ,EAAE;EACnB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;EACjC,OAAQ,IAAI,CAAC;CACb,CAAC;;CCvsBFA,IAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;;CAEpC,IAAM,MAAM,GAC1B,eAAW,CAAC,OAAY,EAAE;mCAAP,GAAG;;EACtB,IAAK,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;EACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;EAC5E,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EAClB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;EACxB,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;CACvC,EAAC;;CAEF,iBAAC,gCAAU,MAAM,EAAE;EACjB,IAAI,MAAM,YAAY,WAAW,EAAE;GAClC,OAAO,IAAI,CAAC,SAAS,CAAC;IACtB,OAAQ,EAAE,MAAM;IACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;IACzB,SAAS,EAAE,IAAI,CAAC,SAAS;IACzB,CAAC,CAAC;GACH;;EAEF,IAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;GACzC,MAAM,IAAI,KAAK,CAAC,sIAAsI,CAAC,CAAC;GACxJ;;EAED,CAAC,UAAU,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,WAAC,QAAO;GAClE,IAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAC;GAC9E,CAAC,CAAC;;EAEH,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;;GAEnC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;GAClC;;EAED,IAAI,MAAM,CAAC,QAAQ,EAAE;GACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;IACxE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IAC/E,IAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzF,MAAM;IACNA,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5F,IAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;KACtD,MAAO,IAAI,KAAK,uCAAmC,MAAM,CAAC,SAAQ,4BAAwB,CAAC;KAC1F;IACD;GACD;;EAEF,IAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAC3B,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,0BAAO,GAAG,EAAE,OAAO,EAAE;EACrB,IAAK,CAAC,SAAS,CAAC;GACd,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;GAC9B,SAAU,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;GAC/C,CAAC,CAAC;;EAEJ,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,0BAAQ;EACPA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;GACzB,KAAK,EAAE,IAAI,CAAC,KAAK;GACjB,SAAS,EAAE,IAAI,CAAC,SAAS;GACzB,CAAC,CAAC;;EAEJ,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;GAC5B,MAAO,CAAC,SAAS,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC,QAAQ;IACzB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;IAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;IAC3B,CAAC,CAAC;GACH,CAAC,CAAC;;EAEJ,OAAQ,MAAM,CAAC;CACf,EAAC;;CAEF,iBAAC,kDAAmB,OAAY,EAAE;;oCAAP,GAAG;;EAC5BA,IAAM,KAAK,GAAG,EAAE,CAAC;EAClB,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;GAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,WAAC,MAAK;IACpD,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;IAC5C,CAAC,CAAC;GACH,CAAC,CAAC;;EAEJ,IAAO,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;EAE7C,IAAI,IAAI,CAAC,KAAK,EAAE;GAChB,QAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;GAC7B;;EAEF,IAAK,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAE;GAChC,IAAI,CAAC,GAAG,CAAC,EAAE;IACX,QAAS,CAAC,OAAO,CAACE,MAAI,CAAC,SAAS,CAAC,CAAC;IACjC;;GAEDF,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAGE,MAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;GAC7FF,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;GACpC,IAAO,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;GAEhD,IAAI,WAAW,CAAC,KAAK,EAAE;IACvB,QAAS,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACpC;;GAEF,WAAY,CAAC,UAAU,CAAC,QAAQ,WAAC,OAAM;IACtC,IAAO,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;IAEhC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;;IAEtD,IAAI,MAAM,CAAC,QAAQ,EAAE;KACpB,IAAI,KAAK,CAAC,MAAM,EAAE;MAClB,QAAS,CAAC,OAAO;OACf,WAAW;OACZ,KAAM,CAAC,OAAO;OACb,GAAG;OACH,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;OACpD,CAAC;MACF,MAAM;MACP,QAAS,CAAC,gBAAgB;OACxB,WAAW;OACX,KAAK;OACN,WAAY,CAAC,QAAQ;OACpB,GAAG;OACJ,WAAY,CAAC,kBAAkB;OAC9B,CAAC;MACF;KACD,MAAM;KACP,QAAS,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAChC;;IAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;IACtD,CAAC,CAAC;;GAEH,IAAI,WAAW,CAAC,KAAK,EAAE;IACvB,QAAS,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACpC;GACD,CAAC,CAAC;;EAEH,OAAO;GACN,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;GAC9D,OAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAC,QAAO;IACtC,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvF,CAAC;GACH,cAAe,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAC,QAAO;IAC9C,OAAQ,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;IACtD,CAAC;GACH,OAAC,KAAK;GACL,QAAQ,EAAE,QAAQ,CAAC,GAAG;GACtB,CAAC;CACH,EAAC;;CAEF,iBAAC,oCAAY,OAAO,EAAE;EACrB,OAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;CACxD,EAAC;;CAEF,iBAAC,8CAAkB;EACjBA,IAAM,kBAAkB,GAAG,EAAE,CAAC;;EAE/B,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;GAC5B,IAAO,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;;GAE3C,IAAI,SAAS,KAAK,IAAI,IAAE,SAAO;;GAE/B,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAC;GACtE,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;GACnC,CAAC,CAAC;;EAEH;GACC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,CAAC,EAAE;IAC5C,OAAQ,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;IACZ;CACH,EAAC;;CAEF,iBAAC,0BAAO,SAAS,EAAE;;;EACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;GACtB,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;GACnC;;EAED,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;;EAEnC,IAAK,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;;EAEpE,IAAK,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAE;GAChCA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,MAAI,CAAC,SAAS,CAAC;GACrFF,IAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;GAE3E,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;IAChC,OAAO,EAAE,MAAM,CAAC,qBAAqB;IACtC,aAAC,WAAW;IACX,CAAC,CAAC;;GAEJ,eAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;GACrD,CAAC,CAAC;;EAEH,IAAI,IAAI,CAAC,KAAK,EAAE;GAChB,IAAK,CAAC,KAAK;IACT,SAAS;IACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,YAAG,KAAK,EAAE,KAAK,EAAE;KAC9C,OAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;KAC7C,CAAC,CAAC;GACJ;;EAEF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,4BAAQ,GAAG,EAAE;EACb,IAAK,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;EAC/B,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,gCAAW;;;EACVA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO;IACvB,GAAG,WAAE,MAAM,EAAE,CAAC,EAAE;IAChBA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,MAAI,CAAC,SAAS,CAAC;IACtF,IAAO,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;;IAElE,OAAQ,GAAG,CAAC;IACX,CAAC;IACD,IAAI,CAAC,EAAE,CAAC,CAAC;;EAEX,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;CAC1B,EAAC;;CAEF,iBAAC,OAAO,uBAAI;EACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;GAC1C,EAAC,OAAO,KAAK,GAAC;EACd,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,WAAC,QAAO,SAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,KAAE,CAAC;GAC1D,EAAC,OAAO,KAAK,GAAC;EACf,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,4BAAS;EACR,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,WAAE,MAAM,EAAE,MAAM,EAAE,SAAG,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,KAAE,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;CACrG,EAAC;;CAEF,iBAAC,kCAAY;EACX,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC9B,EAAC;;CAEF,iBAAC,sBAAK,QAAQ,EAAE;EACd,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CACnD,EAAC;;CAEF,iBAAC,gCAAU,QAAQ,EAAE;EACnBF,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;EACvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAExC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;GACjB,IAAK,MAAM,CAAC;GACXC,IAAI,CAAC,GAAG,CAAC,CAAC;;GAEV,GAAG;IACH,MAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,IAAK,CAAC,MAAM,EAAE;KACZ,MAAM;KACN;IACD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;GACrD;;EAEF,OAAQ,IAAI,CAAC;CACb,EAAC;;CAEF,iBAAC,4BAAQ,QAAQ,EAAE;EACjBD,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;;EAEnD,IAAK,MAAM,CAAC;EACZ,IAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;;EAEhC,GAAG;GACH,MAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;GAC5B,IAAK,CAAC,MAAM,EAAE;IACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACxC,MAAM;IACN;GACD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;;EAEpD,OAAQ,IAAI,CAAC;CACb,CAAC;;CCvRF,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;CAC5B,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;CAClC,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC;;;;;;;;"}