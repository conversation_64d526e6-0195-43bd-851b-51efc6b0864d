{"version": 3, "file": "magic-string.es.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\t// we make these non-enumerable, for sanity while debugging\n\t\tObject.defineProperties(this, {\n\t\t\tprevious: { writable: true, value: null },\n\t\t\tnext:     { writable: true, value: null }\n\t\t});\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.end - trimmed.length);\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "import { encode } from 'sourcemap-codec';\n\nlet btoa = () => {\n\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n};\nif (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n\tbtoa = str => window.btoa(unescape(encodeURIComponent(str)));\n} else if (typeof Buffer === 'function') {\n\tbtoa = str => Buffer.from(str, 'utf-8').toString('base64');\n}\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter(line => /^\\t+/.test(line));\n\tconst spaced = lines.filter(line => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "export default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t}\n\n\t\tthis.advance(content);\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\tthis.rawSegments.push([this.generatedCodeColumn, sourceIndex, loc.line, loc.column]);\n\t\t\t}\n\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t} else {\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal:              { writable: true, value: string },\n\t\t\toutro:                 { writable: true, value: '' },\n\t\t\tintro:                 { writable: true, value: '' },\n\t\t\tfirstChunk:            { writable: true, value: chunk },\n\t\t\tlastChunk:             { writable: true, value: chunk },\n\t\t\tlastSearchedChunk:     { writable: true, value: chunk },\n\t\t\tbyStart:               { writable: true, value: {} },\n\t\t\tbyEnd:                 { writable: true, value: {} },\n\t\t\tfilename:              { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations:    { writable: true, value: new BitSet() },\n\t\t\tstoredNames:           { writable: true, value: {} },\n\t\t\tindentStr:             { writable: true, value: guessIndent(string) }\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext(chunk => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: [options.source ? getRelativePath(options.file || '', options.source) : null],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : [null],\n\t\t\tnames,\n\t\t\tmappings: mappings.raw\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tindentStr = indentStr !== undefined ? indentStr : this.indentStr || '\\t';\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach(exclusion => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = match => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error('magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)');\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn('magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead'); // eslint-disable-line no-console\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn('magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead'); // eslint-disable-line no-console\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error('Cannot overwrite a zero-length range – use appendLeft or prependRight instead');\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn('The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string'); // eslint-disable-line no-console\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst contentOnly = options !== undefined ? options.contentOnly : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tthis.storedNames[original] = true;\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tif (end > first.end && first.next !== this.byStart[first.end]) {\n\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, contentOnly);\n\n\t\t\tif (first !== last) {\n\t\t\t\tlet chunk = first.next;\n\t\t\t\twhile (chunk !== last) {\n\t\t\t\t\tchunk.edit('', false);\n\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length)\n\t\t\treturn this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length)\n\t\t\t\treturn chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length)\n\t\t\t\treturn chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length)\n\t\t\t\treturn chunk.intro[chunk.intro.length - 1];\n\t\t} while (chunk = chunk.previous);\n\t\tif (this.intro.length)\n\t\t\treturn this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1)\n\t\t\treturn this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1)\n\t\t\t\t\treturn chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while (chunk = chunk.previous);\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1)\n\t\t\treturn this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length) {\n\t\twhile (start < 0) start += this.original.length;\n\t\twhile (end < 0) end += this.original.length;\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${\n\t\t\t\t\tchunk.original\n\t\t\t\t}\")`\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (chunk.intro.length && chunk.intro.trim() ||\n\t\t\t\t\tchunk.content.length && chunk.content.trim() ||\n\t\t\t\t\tchunk.outro.length && chunk.outro.trim())\n\t\t\t\treturn false;\n\t\t} while (chunk = chunk.next);\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while (chunk = chunk.next);\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error('bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`');\n\t\t}\n\n\t\t['filename', 'indentExclusionRanges', 'separator'].forEach(option => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || ''\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator\n\t\t});\n\n\t\tthis.sources.forEach(source => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tthis.sources.forEach(source => {\n\t\t\tObject.keys(source.content.storedNames).forEach(name => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext(chunk => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : null,\n\t\t\tsources: this.uniqueSources.map(source => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map(source => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach(source => {\n\t\t\tconst indentStr = source.content.indentStr;\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty () {\n\t\tif (this.intro.length && this.intro.trim())\n\t\t\treturn false;\n\t\tif (this.sources.some(source => !source.content.isEmpty()))\n\t\t\treturn false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce((length, source) => length + source.content.length(), this.intro.length);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n"], "names": ["const", "let", "this"], "mappings": ";;AAAe,IAAM,MAAM,GAC1B,eAAW,CAAC,GAAG,EAAE;CAChB,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;EAC1D;;AAEF,iBAAC,oBAAI,CAAC,EAAE;CACN,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;EACnC;;AAEF,iBAAC,oBAAI,CAAC,EAAE;CACP,OAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/C;;ACXa,IAAM,KAAK,GACzB,cAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;CAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;CACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;;CAExB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;CAEhB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;CACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;CACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;;;CAGpB,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;EAC9B,QAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;EAC1C,IAAK,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;EACzC,CAAC,CAAC;EACH;;AAEF,gBAAC,kCAAW,OAAO,EAAE;CACnB,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;EACtB;;AAEF,gBAAC,oCAAY,OAAO,EAAE;CACrB,IAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;EAClC;;AAEF,gBAAC,0BAAQ;CACPA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;CAE7D,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CACzB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CACzB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CAC7B,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACjC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;;CAE5B,OAAQ,KAAK,CAAC;EACb;;AAEF,gBAAC,8BAAS,KAAK,EAAE;CACf,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC;EAC9C;;AAEF,gBAAC,8BAAS,EAAE,EAAE;CACZC,IAAI,KAAK,GAAG,IAAI,CAAC;CAClB,OAAQ,KAAK,EAAE;EACb,EAAE,CAAC,KAAK,CAAC,CAAC;EACV,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB;EACD;;AAEF,gBAAC,sCAAa,EAAE,EAAE;CAChBA,IAAI,KAAK,GAAG,IAAI,CAAC;CAClB,OAAQ,KAAK,EAAE;EACb,EAAE,CAAC,KAAK,CAAC,CAAC;EACV,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;EACvB;EACD;;AAEF,gBAAC,sBAAK,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;CACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;CACxB,IAAK,CAAC,WAAW,EAAE;EACjB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;EAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;EAChB;CACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;CAE3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;;CAEpB,OAAQ,IAAI,CAAC;EACZ;;AAEF,gBAAC,oCAAY,OAAO,EAAE;CACrB,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAClC;;AAEF,gBAAC,sCAAa,OAAO,EAAE;CACtB,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAClC;;AAEF,gBAAC,wBAAM,KAAK,EAAE;CACb,IAAO,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;CAEtCD,IAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;CAC1DA,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;;CAEtD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;;CAE/BA,IAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;CAC3D,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC5B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;CAEhB,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;;CAEjB,IAAI,IAAI,CAAC,MAAM,EAAE;;EAEjB,QAAS,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;EACzB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EAClB,MAAM;EACN,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;EAC9B;;CAED,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAC1B,IAAI,QAAQ,CAAC,IAAI,IAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAC;CACrD,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;CACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;;CAEtB,OAAQ,QAAQ,CAAC;EAChB;;AAEF,gBAAC,gCAAW;CACV,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC9C;;AAEF,gBAAC,4BAAQ,EAAE,EAAE;CACX,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;CAEnCA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;CAE7C,IAAI,OAAO,CAAC,MAAM,EAAE;EACnB,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;GAC9B,IAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;GAClE;EACF,OAAQ,IAAI,CAAC;;EAEZ,MAAM;EACP,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;EAE/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;EACnC;EACD;;AAEF,gBAAC,gCAAU,EAAE,EAAE;CACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;CAEnCA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;CAE7C,IAAI,OAAO,CAAC,MAAM,EAAE;EACnB,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;GAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;GACvC,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;GAC/B;EACF,OAAQ,IAAI,CAAC;;EAEZ,MAAM;EACP,IAAK,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;;EAE/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;EACnC;CACD;;ACxJFC,IAAI,IAAI,eAAM;CACb,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;CAC3F,CAAC;AACF,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE;CACvE,IAAI,aAAG,KAAI,SAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAC,CAAC;CAC7D,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;CACxC,IAAI,aAAG,KAAI,SAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAC,CAAC;CAC3D;;AAEc,IAAM,SAAS,GAC7B,kBAAW,CAAC,UAAU,EAAE;CACvB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;CACjB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;CAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;CAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;CAChD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;CAC/B,IAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;EAC5C;;AAEF,oBAAC,gCAAW;CACV,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EAC5B;;AAEF,oBAAC,0BAAQ;CACR,OAAQ,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC7E;;AC3Ba,SAAS,WAAW,CAAC,IAAI,EAAE;CACzCD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;CAE/BA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAC,MAAK,SAAG,MAAM,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;CACvDA,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,WAAC,MAAK,SAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAC,CAAC,CAAC;;CAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EAC/C,OAAO,IAAI,CAAC;EACZ;;;;;CAKD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;EACnC,OAAO,IAAI,CAAC;EACZ;;;CAGDA,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,WAAE,QAAQ,EAAE,OAAO,EAAE;EAC7CA,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;EAChD,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;EACrC,EAAE,QAAQ,CAAC,CAAC;;CAEb,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACpC;;ACxBc,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;CACjDA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;CACtCA,IAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;CAElC,SAAS,CAAC,GAAG,EAAE,CAAC;;CAEhB,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;EACnC,SAAS,CAAC,KAAK,EAAE,CAAC;EAClB,OAAO,CAAC,KAAK,EAAE,CAAC;EAChB;;CAED,IAAI,SAAS,CAAC,MAAM,EAAE;EACrBC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;EACzB,OAAO,CAAC,EAAE,IAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAC;EAChC;;CAED,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAC3C;;ACjBDD,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;;AAE3C,AAAe,SAAS,QAAQ,CAAC,KAAK,EAAE;CACvC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;CAClD;;ACJc,SAAS,UAAU,CAAC,MAAM,EAAE;CAC1CA,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CACzCA,IAAM,WAAW,GAAG,EAAE,CAAC;;CAEvB,KAAKC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACvD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACtB,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;EACnC;;CAED,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;EAC7BA,IAAI,CAAC,GAAG,CAAC,CAAC;EACVA,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;EAC3B,OAAO,CAAC,GAAG,CAAC,EAAE;GACbD,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;GACvB,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,GAAG,CAAC,CAAC;IACN,MAAM;IACN,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACV;GACD;EACDA,IAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACnBA,IAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;EACzC,OAAO,QAAE,IAAI,UAAE,MAAM,EAAE,CAAC;EACxB,CAAC;CACF;;ACxBc,IAAM,QAAQ,GAC5B,iBAAW,CAAC,KAAK,EAAE;CAClB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACnB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;CAC3B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CAC7B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;CACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;CACzD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACpB;;AAEF,mBAAC,4BAAQ,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;CAC7C,IAAI,OAAO,CAAC,MAAM,EAAE;EACnBA,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;EAC9E,IAAI,SAAS,IAAI,CAAC,EAAE;GACnB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;GACxB;EACF,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC/B,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;EACzB,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACpC;;CAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;CACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACpB;;AAEF,mBAAC,8CAAiB,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;CACvEC,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;CACpCA,IAAI,KAAK,GAAG,IAAI,CAAC;;CAEjB,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;EACrC,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;GACtE,IAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;GACrF;;EAED,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;GACzC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;GACd,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;GACf,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;GAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;GACzD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;GAC9B,KAAM,GAAG,IAAI,CAAC;GACb,MAAM;GACN,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;GAChB,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;GAC/B,KAAM,GAAG,KAAK,CAAC;GACd;;EAEF,iBAAkB,IAAI,CAAC,CAAC;EACvB;;CAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACpB;;AAEF,mBAAC,4BAAQ,GAAG,EAAE;CACZ,IAAI,CAAC,GAAG,IAAE,SAAO;;CAElB,IAAO,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;CAE9B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;EACrB,KAAKA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;GAC1C,IAAI,CAAC,iBAAiB,EAAE,CAAC;GACzB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;GACzD;EACD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;EAC7B;;CAED,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;CAC3D;;ACzDFD,IAAM,CAAC,GAAG,IAAI,CAAC;;AAEfA,IAAM,MAAM,GAAG;CACd,UAAU,EAAE,KAAK;CACjB,WAAW,EAAE,KAAK;CAClB,SAAS,EAAE,KAAK;CAChB,CAAC;;AAEF,IAAqB,WAAW,GAC/B,oBAAW,CAAC,MAAM,EAAE,OAAY,EAAE;kCAAP,GAAG;;CAC7BA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;;CAElD,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;EAC9B,QAAS,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;EACzD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;EACrD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;EACrD,UAAW,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;EACxD,SAAU,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;EACxD,iBAAkB,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;EACxD,OAAQ,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;EACrD,KAAM,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;EACpD,QAAQ,eAAe,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;EAClE,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;EAC/E,kBAAkB,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;EAC/D,WAAY,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;EACpD,SAAS,cAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE;EACrE,CAAC,CAAC;;CAMJ,IAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;CACzB,IAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;EAClC;;AAEF,sBAAC,sDAAqB,IAAI,EAAE;CAC3B,IAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAClC;;AAEF,sBAAC,0BAAO,OAAO,EAAE;CACf,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;;CAEvF,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;CACvB,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,kCAAW,KAAK,EAAE,OAAO,EAAE;CAC1B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;CAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;CAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;CAEjC,IAAK,KAAK,EAAE;EACV,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC1B,MAAM;EACN,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;EACtB;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;CAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;CAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;CAEnC,IAAK,KAAK,EAAE;EACV,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EAC3B,MAAM;EACN,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC;EACtB;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,0BAAQ;CACPA,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;;CAE3EC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC;CACpCA,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;;CAE1F,OAAQ,aAAa,EAAE;EACtB,MAAO,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;EACjD,MAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;;EAE5CD,IAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAC;EAC9C,IAAO,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAC;;EAExE,IAAK,eAAe,EAAE;GACpB,WAAW,CAAC,IAAI,GAAG,eAAe,CAAC;GACnC,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAC;;GAExC,WAAY,GAAG,eAAe,CAAC;GAC9B;;EAEF,aAAc,GAAG,iBAAiB,CAAC;EAClC;;CAED,MAAM,CAAC,SAAS,GAAG,WAAW,CAAC;;CAE/B,IAAI,IAAI,CAAC,qBAAqB,EAAE;EAChC,MAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;EAClE;;CAEF,MAAO,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;CAEhE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;CAC1B,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;;CAE3B,OAAQ,MAAM,CAAC;EACd;;AAEF,sBAAC,kDAAmB,OAAO,EAAE;;;CAC3B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;;CAExBA,IAAM,WAAW,GAAG,CAAC,CAAC;CACtBA,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;CAC7C,IAAO,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;CAE9C,IAAO,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;CAEzC,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,QAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC7B;;CAEF,IAAK,CAAC,UAAU,CAAC,QAAQ,WAAC,OAAM;EAC/B,IAAO,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;EAEhC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;;EAEtD,IAAI,KAAK,CAAC,MAAM,EAAE;GAClB,QAAS,CAAC,OAAO;IACf,WAAW;IACZ,KAAM,CAAC,OAAO;IACb,GAAG;IACH,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACpD,CAAC;GACF,MAAM;GACN,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAEE,MAAI,CAAC,QAAQ,EAAE,GAAG,EAAEA,MAAI,CAAC,kBAAkB,CAAC,CAAC;GAC3F;;EAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;EACtD,CAAC,CAAC;;CAEH,OAAO;EACN,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;EAC9D,OAAQ,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;EACtF,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC;EAClE,OAAC,KAAK;EACL,QAAQ,EAAE,QAAQ,CAAC,GAAG;EACtB,CAAC;EACF;;AAEF,sBAAC,oCAAY,OAAO,EAAE;CACrB,OAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD;;AAEF,sBAAC,8CAAkB;CACjB,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;EACvD;;AAEF,sBAAC,0BAAO,SAAS,EAAE,OAAO,EAAE;CAC1BF,IAAM,OAAO,GAAG,YAAY,CAAC;;CAE7B,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;EACzB,OAAQ,GAAG,SAAS,CAAC;EACrB,SAAU,GAAG,SAAS,CAAC;EACtB;;CAED,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;;CAEzE,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;;CAElC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;;;CAGxBA,IAAM,UAAU,GAAG,EAAE,CAAC;;CAEtB,IAAI,OAAO,CAAC,OAAO,EAAE;EACrB,IAAO,UAAU;GACf,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;EAC9E,UAAU,CAAC,OAAO,WAAC,WAAU;GAC7B,KAAMC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;IACpD,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACrB;GACD,CAAC,CAAC;EACH;;CAEF,IAAK,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;CAC9DD,IAAM,QAAQ,aAAG,OAAM;EACvB,IAAK,yBAAyB,IAAE,aAAU,SAAS,GAAG,KAAK,IAAG;EAC9D,yBAA0B,GAAG,IAAI,CAAC;EAClC,OAAQ,KAAK,CAAC;EACb,CAAC;;CAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;CAEnDC,IAAI,SAAS,GAAG,CAAC,CAAC;CAClBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;;CAE7B,OAAQ,KAAK,EAAE;EACbD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;;EAEtB,IAAI,KAAK,CAAC,MAAM,EAAE;GACjB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;IAC3B,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;IAEzD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;KACzB,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC;KAC7E;IACD;GACD,MAAM;GACN,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC;;GAExB,OAAO,SAAS,GAAG,GAAG,EAAE;IACvB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;KAC5B,IAAO,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;;KAEtC,IAAI,IAAI,KAAK,IAAI,EAAE;MACnB,yBAA0B,GAAG,IAAI,CAAC;MACjC,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;MACvD,yBAA0B,GAAG,KAAK,CAAC;;MAElC,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;OAC9B,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;OAC9B,MAAM;OACP,IAAK,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;OACnC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;OACnB,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;OAC9B;MACD;KACD;;IAEF,SAAU,IAAI,CAAC,CAAC;IACf;GACD;;EAED,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC;EACtB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB;;CAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;CAEpD,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,4BAAS;CACR,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;EACnG;;AAEF,sBAAC,kCAAW,KAAK,EAAE,OAAO,EAAE;CAC1B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;EACvB,OAAO,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;EACnG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;EACzB;;CAEF,OAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EACvC;;AAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC3B,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;EACxB,OAAO,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC;EACtG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;EAC1B;;CAEF,OAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;EACzC;;AAEF,sBAAC,sBAAK,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;CACvB,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,GAAC;;CAI7F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;CACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;CAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACnC,IAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;CAE7BA,IAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC;CAC/BA,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;;CAE5B,IAAO,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACrC,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,IAAE,OAAO,IAAI,GAAC;CACtDA,IAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;;CAE/D,IAAK,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,GAAC;CACtC,IAAK,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,GAAC;;CAE3C,IAAK,OAAO,IAAE,OAAO,CAAC,IAAI,GAAG,KAAK,GAAC;CACnC,IAAK,QAAQ,IAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,GAAC;;CAEvC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,GAAC;CACjD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;EACf,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;EAChC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;EAC3B;;CAED,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;CACzB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;;CAE9B,IAAK,CAAC,OAAO,IAAE,IAAI,CAAC,UAAU,GAAG,KAAK,GAAC;CACvC,IAAK,CAAC,QAAQ,IAAE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAC;CAGtC,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,gCAAU,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;CACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,GAAC;;CAE7F,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;CAE5C,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,GAAC;CACzE,IAAK,KAAK,KAAK,GAAG;EACjB,EAAC,MAAM,IAAI,KAAK,CAAC,+EAA+E,CAAC,GAAC;;CAIlG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;;CAEjB,IAAI,OAAO,KAAK,IAAI,EAAE;EACrB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;GACtB,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;GAC9I,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;GACxB;;EAED,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;EAC9B;CACDA,IAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;CACpEA,IAAM,WAAW,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;;CAEzE,IAAK,SAAS,EAAE;EACdA,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;EAClD,IAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;EAClC;;CAEF,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CACnC,IAAO,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;CAE9B,IAAK,KAAK,EAAE;EACV,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;GAC9D,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;GACzD;;EAEF,KAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;;EAE5C,IAAI,KAAK,KAAK,IAAI,EAAE;GACnBC,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;GACvB,OAAO,KAAK,KAAK,IAAI,EAAE;IACvB,KAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACtB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;IACnB;;GAEF,KAAM,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;GACtB;EACD,MAAM;;EAEP,IAAO,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;;;EAGpE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;EACrB,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;EACzB;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,4BAAQ,OAAO,EAAE;CAChB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,GAAC;;CAExF,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACnC,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,oCAAY,KAAK,EAAE,OAAO,EAAE;CAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;CAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;CAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;CAEjC,IAAK,KAAK,EAAE;EACV,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EAC3B,MAAM;EACP,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAClC;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,sCAAa,KAAK,EAAE,OAAO,EAAE;CAC5B,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,GAAC;;CAI1F,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;CAEpB,IAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;CAEnC,IAAK,KAAK,EAAE;EACV,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;EAC5B,MAAM;EACP,IAAK,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAClC;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,0BAAO,KAAK,EAAE,GAAG,EAAE;CAClB,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;CAE5C,IAAI,KAAK,KAAK,GAAG,IAAE,OAAO,IAAI,GAAC;;CAE/B,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,GAAC;CAC5F,IAAK,KAAK,GAAG,GAAG,IAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,GAAC;;CAInE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;;CAElB,IAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;CAEjC,OAAQ,KAAK,EAAE;EACb,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;EACjB,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;EACjB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;EAEf,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;EACzD;CAGF,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,gCAAW;CACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;EACrB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CAC1CA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;CAC3B,GAAG;EACF,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM;GACtB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;EAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM;GACxB,EAAC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;EAChD,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM;GACtB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;EAC5C,QAAQ,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE;CACjC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;EACrB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC;CAC3C,OAAQ,EAAE,CAAC;EACV;;AAEF,sBAAC,gCAAW;CACVA,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC1C,IAAI,SAAS,KAAK,CAAC,CAAC;EACpB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAC;CACzCA,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;CACzBA,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;CAC3B,GAAG;EACH,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;GAC5B,SAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;GACvC,IAAI,SAAS,KAAK,CAAC,CAAC;IACpB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;GACpD,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;GAChC;;EAEF,IAAK,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;GAC9B,SAAU,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;GACzC,IAAI,SAAS,KAAK,CAAC,CAAC;IACpB,EAAC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;GACtD,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;GAClC;;EAEF,IAAK,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;GAC5B,SAAU,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;GACvC,IAAI,SAAS,KAAK,CAAC,CAAC;IACpB,EAAC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;GACpD,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;GAChC;EACD,QAAQ,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE;CAClC,SAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACtC,IAAI,SAAS,KAAK,CAAC,CAAC;EACpB,EAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,GAAC;CACnD,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;EAC5B;;AAEF,sBAAC,wBAAM,KAAS,EAAE,GAA0B,EAAE;+BAAlC,GAAG;2BAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;CACpC,OAAO,KAAK,GAAG,CAAC,IAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;CAChD,OAAO,GAAG,GAAG,CAAC,IAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAC;;CAE5CA,IAAI,MAAM,GAAG,EAAE,CAAC;;;CAGhBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC5B,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;;EAE5D,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;GAC3C,OAAQ,MAAM,CAAC;GACd;;EAED,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB;;CAEF,IAAK,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;EAClD,EAAC,MAAM,IAAI,KAAK,qCAAkC,KAAK,8BAA0B,GAAC;;CAElFD,IAAM,UAAU,GAAG,KAAK,CAAC;CAC1B,OAAQ,KAAK,EAAE;EACb,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;GACnE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;GACtB;;EAEDA,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;EAC3D,IAAK,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;GACpD,EAAC,MAAM,IAAI,KAAK,qCAAkC,GAAG,4BAAwB,GAAC;;EAE9EA,IAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;EACnE,IAAO,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;;EAE7F,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;;EAEpD,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;GACvD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;GACtB;;EAEF,IAAK,WAAW,EAAE;GAChB,MAAM;GACN;;EAED,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB;;CAEF,OAAQ,MAAM,CAAC;EACd;;;AAGF,sBAAC,sBAAK,KAAK,EAAE,GAAG,EAAE;CACjB,IAAO,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;CAC5B,KAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CACvB,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;;CAE1C,OAAQ,KAAK,CAAC;EACb;;AAEF,sBAAC,0BAAO,KAAK,EAAE;CACb,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAE,SAAO;;CAIrDC,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;CACpC,IAAO,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;;CAEzC,OAAQ,KAAK,EAAE;EACb,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,GAAC;;EAElE,KAAM,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EAC1E;EACD;;AAEF,sBAAC,oCAAY,KAAK,EAAE,KAAK,EAAE;CAC1B,IAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;;EAEzCD,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;EAC9C,MAAO,IAAI,KAAK;6DACwC,GAAG,CAAC,KAAI,UAAI,GAAG,CAAC,OAAM,cAC3E,KAAK,CAAC,SAAQ;GAEf,CAAC;EACF;;CAEF,IAAO,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;CAErC,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;CAC3B,IAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;CAChC,IAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;;CAEpC,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAC;;CAExD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;CAEhC,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,gCAAW;CACVC,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;;CAErBA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC7B,OAAQ,KAAK,EAAE;EACb,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;EACxB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB;;CAED,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;EACxB;;AAEF,sBAAC,8BAAU;CACTA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC5B,GAAG;EACF,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;IAC3C,KAAM,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;IAC7C,KAAM,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;GAC1C,EAAC,OAAO,KAAK,GAAC;EACd,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;CAC9B,OAAQ,IAAI,CAAC;EACZ;;AAEF,sBAAC,4BAAS;CACRA,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;CAC5BA,IAAI,MAAM,GAAG,CAAC,CAAC;CACf,GAAG;EACF,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;EACzE,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;CAC9B,OAAQ,MAAM,CAAC;EACd;;AAEF,sBAAC,kCAAY;CACX,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC7B;;AAEF,sBAAC,sBAAK,QAAQ,EAAE;CACd,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;EAClD;;AAEF,sBAAC,0CAAe,QAAQ,EAAE;CACxBD,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;;CAElD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;CAEnCC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;;CAE3B,GAAG;EACFD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;EACvB,IAAO,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;;EAGlC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;GACtB,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;IAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;IAC5B;;GAEF,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;GAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;GAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;GACxC;;EAED,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;EACzB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;EACvB,QAAQ,KAAK,EAAE;;CAEjB,OAAQ,KAAK,CAAC;EACb;;AAEF,sBAAC,4BAAQ,QAAQ,EAAE;CACjB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;CAC/B,OAAQ,IAAI,CAAC;EACZ;AACF,sBAAC,8CAAiB,QAAQ,EAAE;CAC1BA,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;;CAEvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CACzC,IAAK,IAAI,CAAC,KAAK,CAAC,MAAM,IAAE,OAAO,IAAI,GAAC;;CAEnCC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;;CAE5B,GAAG;EACFD,IAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;EACvB,IAAO,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;;EAEpC,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;;GAEtB,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,IAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,GAAC;;GAE3D,IAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;GAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;GAC5C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;GACxC;;EAED,IAAI,OAAO,IAAE,OAAO,IAAI,GAAC;EACzB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;EACnB,QAAQ,KAAK,EAAE;;CAEjB,OAAQ,KAAK,CAAC;EACb;;AAEF,sBAAC,gCAAU,QAAQ,EAAE;CACnB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;CACjC,OAAQ,IAAI,CAAC;CACZ;;ACvsBFA,IAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;;AAEpC,IAAM,MAAM,GAC1B,eAAW,CAAC,OAAY,EAAE;kCAAP,GAAG;;CACtB,IAAK,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;CACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;CAC5E,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CAClB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;CACxB,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;EACtC;;AAEF,iBAAC,gCAAU,MAAM,EAAE;CACjB,IAAI,MAAM,YAAY,WAAW,EAAE;EAClC,OAAO,IAAI,CAAC,SAAS,CAAC;GACtB,OAAQ,EAAE,MAAM;GACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;GACzB,SAAS,EAAE,IAAI,CAAC,SAAS;GACzB,CAAC,CAAC;EACH;;CAEF,IAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;EACzC,MAAM,IAAI,KAAK,CAAC,sIAAsI,CAAC,CAAC;EACxJ;;CAED,CAAC,UAAU,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,WAAC,QAAO;EAClE,IAAK,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAC;EAC9E,CAAC,CAAC;;CAEH,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;;EAEnC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAClC;;CAED,IAAI,MAAM,CAAC,QAAQ,EAAE;EACpB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;GACxE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;GAC/E,IAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;GACzF,MAAM;GACNA,IAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;GAC5F,IAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;IACtD,MAAO,IAAI,KAAK,uCAAmC,MAAM,CAAC,SAAQ,4BAAwB,CAAC;IAC1F;GACD;EACD;;CAEF,IAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC3B,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,0BAAO,GAAG,EAAE,OAAO,EAAE;CACrB,IAAK,CAAC,SAAS,CAAC;EACd,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;EAC9B,SAAU,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;EAC/C,CAAC,CAAC;;CAEJ,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,0BAAQ;CACPA,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;EACzB,KAAK,EAAE,IAAI,CAAC,KAAK;EACjB,SAAS,EAAE,IAAI,CAAC,SAAS;EACzB,CAAC,CAAC;;CAEJ,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;EAC5B,MAAO,CAAC,SAAS,CAAC;GAChB,QAAQ,EAAE,MAAM,CAAC,QAAQ;GACzB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;GAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;GAC3B,CAAC,CAAC;EACH,CAAC,CAAC;;CAEJ,OAAQ,MAAM,CAAC;EACd;;AAEF,iBAAC,kDAAmB,OAAY,EAAE;;mCAAP,GAAG;;CAC5BA,IAAM,KAAK,GAAG,EAAE,CAAC;CAClB,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;EAC3B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,WAAC,MAAK;GACpD,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAC;GAC5C,CAAC,CAAC;EACH,CAAC,CAAC;;CAEJ,IAAO,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;CAE7C,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,QAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC7B;;CAEF,IAAK,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAE;EAChC,IAAI,CAAC,GAAG,CAAC,EAAE;GACX,QAAS,CAAC,OAAO,CAACE,MAAI,CAAC,SAAS,CAAC,CAAC;GACjC;;EAEDF,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAGE,MAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7FF,IAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;EACpC,IAAO,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;EAEhD,IAAI,WAAW,CAAC,KAAK,EAAE;GACvB,QAAS,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;GACpC;;EAEF,WAAY,CAAC,UAAU,CAAC,QAAQ,WAAC,OAAM;GACtC,IAAO,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;;GAEhC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;;GAEtD,IAAI,MAAM,CAAC,QAAQ,EAAE;IACpB,IAAI,KAAK,CAAC,MAAM,EAAE;KAClB,QAAS,CAAC,OAAO;MACf,WAAW;MACZ,KAAM,CAAC,OAAO;MACb,GAAG;MACH,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;MACpD,CAAC;KACF,MAAM;KACP,QAAS,CAAC,gBAAgB;MACxB,WAAW;MACX,KAAK;MACN,WAAY,CAAC,QAAQ;MACpB,GAAG;MACJ,WAAY,CAAC,kBAAkB;MAC9B,CAAC;KACF;IACD,MAAM;IACP,QAAS,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAChC;;GAED,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAC;GACtD,CAAC,CAAC;;EAEH,IAAI,WAAW,CAAC,KAAK,EAAE;GACvB,QAAS,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;GACpC;EACD,CAAC,CAAC;;CAEH,OAAO;EACN,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;EAC9D,OAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAC,QAAO;GACtC,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;GACvF,CAAC;EACH,cAAe,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,WAAC,QAAO;GAC9C,OAAQ,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;GACtD,CAAC;EACH,OAAC,KAAK;EACL,QAAQ,EAAE,QAAQ,CAAC,GAAG;EACtB,CAAC;EACF;;AAEF,iBAAC,oCAAY,OAAO,EAAE;CACrB,OAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD;;AAEF,iBAAC,8CAAkB;CACjBA,IAAM,kBAAkB,GAAG,EAAE,CAAC;;CAE/B,IAAK,CAAC,OAAO,CAAC,OAAO,WAAC,QAAO;EAC5B,IAAO,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;;EAE3C,IAAI,SAAS,KAAK,IAAI,IAAE,SAAO;;EAE/B,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAC;EACtE,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;EACnC,CAAC,CAAC;;CAEH;EACC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,WAAE,CAAC,EAAE,CAAC,EAAE;GAC5C,OAAQ,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;GACrD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;GACZ;EACF;;AAEF,iBAAC,0BAAO,SAAS,EAAE;;;CACjB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;EACtB,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;EACnC;;CAED,IAAI,SAAS,KAAK,EAAE,IAAE,OAAO,IAAI,GAAC;;CAEnC,IAAK,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;;CAEpE,IAAK,CAAC,OAAO,CAAC,OAAO,WAAE,MAAM,EAAE,CAAC,EAAE;EAChCA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,MAAI,CAAC,SAAS,CAAC;EACrFF,IAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;EAE3E,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;GAChC,OAAO,EAAE,MAAM,CAAC,qBAAqB;GACtC,aAAC,WAAW;GACX,CAAC,CAAC;;EAEJ,eAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;EACrD,CAAC,CAAC;;CAEH,IAAI,IAAI,CAAC,KAAK,EAAE;EAChB,IAAK,CAAC,KAAK;GACT,SAAS;GACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,YAAG,KAAK,EAAE,KAAK,EAAE;IAC9C,OAAQ,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;IAC7C,CAAC,CAAC;EACJ;;CAEF,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,4BAAQ,GAAG,EAAE;CACb,IAAK,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;CAC/B,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,gCAAW;;;CACVA,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO;GACvB,GAAG,WAAE,MAAM,EAAE,CAAC,EAAE;GAChBA,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAGE,MAAI,CAAC,SAAS,CAAC;GACtF,IAAO,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;;GAElE,OAAQ,GAAG,CAAC;GACX,CAAC;GACD,IAAI,CAAC,EAAE,CAAC,CAAC;;CAEX,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACzB;;AAEF,iBAAC,OAAO,uBAAI;CACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;EAC1C,EAAC,OAAO,KAAK,GAAC;CACd,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,WAAC,QAAO,SAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,KAAE,CAAC;EAC1D,EAAC,OAAO,KAAK,GAAC;CACf,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,4BAAS;CACR,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,WAAE,MAAM,EAAE,MAAM,EAAE,SAAG,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,KAAE,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;EACpG;;AAEF,iBAAC,kCAAY;CACX,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC7B;;AAEF,iBAAC,sBAAK,QAAQ,EAAE;CACd,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;EAClD;;AAEF,iBAAC,gCAAU,QAAQ,EAAE;CACnBF,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;CACvD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;CAExC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;EACjB,IAAK,MAAM,CAAC;EACXC,IAAI,CAAC,GAAG,CAAC,CAAC;;EAEV,GAAG;GACH,MAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;GAC5B,IAAK,CAAC,MAAM,EAAE;IACZ,MAAM;IACN;GACD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;EACrD;;CAEF,OAAQ,IAAI,CAAC;EACZ;;AAEF,iBAAC,4BAAQ,QAAQ,EAAE;CACjBD,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;;CAEnD,IAAK,MAAM,CAAC;CACZ,IAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;;CAEhC,GAAG;EACH,MAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B,IAAK,CAAC,MAAM,EAAE;GACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;GACxC,MAAM;GACN;EACD,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;;CAEpD,OAAQ,IAAI,CAAC;CACZ;;;;;"}