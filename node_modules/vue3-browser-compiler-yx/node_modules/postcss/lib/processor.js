'use strict'

let LazyResult = require('./lazy-result')
let Root = require('./root')

class Processor {
  constructor (plugins = []) {
    this.version = '8.1.10'
    this.plugins = this.normalize(plugins)
  }

  use (plugin) {
    this.plugins = this.plugins.concat(this.normalize([plugin]))
    return this
  }

  process (css, opts = {}) {
    if (
      this.plugins.length === 0 &&
      opts.parser === opts.stringifier &&
      !opts.hideNothingWarning
    ) {
      if (process.env.NODE_ENV !== 'production') {
        if (typeof console !== 'undefined' && console.warn) {
          console.warn(
            'You did not set any plugins, parser, or stringifier. ' +
              'Right now, PostCSS does nothing. Pick plugins for your case ' +
              'on https://www.postcss.parts/ and use them in postcss.config.js.'
          )
        }
      }
    }
    return new LazyResult(this, css, opts)
  }

  normalize (plugins) {
    let normalized = []
    for (let i of plugins) {
      if (i.postcss === true) {
        i = i()
      } else if (i.postcss) {
        i = i.postcss
      }

      if (typeof i === 'object' && Array.isArray(i.plugins)) {
        normalized = normalized.concat(i.plugins)
      } else if (typeof i === 'object' && i.postcssPlugin) {
        normalized.push(i)
      } else if (typeof i === 'function') {
        normalized.push(i)
      } else if (typeof i === 'object' && (i.parse || i.stringify)) {
        if (process.env.NODE_ENV !== 'production') {
          throw new Error(
            'PostCSS syntaxes cannot be used as plugins. Instead, please use ' +
              'one of the syntax/parser/stringifier options as outlined ' +
              'in your PostCSS runner documentation.'
          )
        }
      } else {
        throw new Error(i + ' is not a PostCSS plugin')
      }
    }
    return normalized
  }
}

module.exports = Processor
Processor.default = Processor

Root.registerProcessor(Processor)
