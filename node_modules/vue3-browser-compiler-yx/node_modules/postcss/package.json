{"name": "postcss", "version": "8.1.10", "description": "Tool for transforming styles with JS plugins", "engines": {"node": "^10 || ^12 || >=14"}, "exports": {".": {"require": "./lib/postcss.js", "import": "./lib/postcss.mjs", "types": "./lib/postcss.d.ts"}, "./": "./"}, "main": "./lib/postcss.js", "types": "./lib/postcss.d.ts", "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://postcss.org/", "repository": "postcss/postcss", "dependencies": {"colorette": "^1.2.1", "nanoid": "^3.1.18", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "browser": {"./lib/terminal-highlight": false, "colorette": false, "fs": false}}