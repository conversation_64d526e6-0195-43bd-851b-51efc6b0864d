{"name": "vue3-browser-compiler-yx", "version": "1.0.4", "main": "dist/compiler-sfc.cjs.js", "types": "dist/compiler-sfc.d.ts", "files": ["dist"], "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "peerDependencies": {"vue": "^2.0.0||^3.0.0"}, "dependencies": {"@babel/parser": "^7.13.9", "@babel/types": "^7.13.0", "@vue/compiler-core": "3.0.11", "@vue/compiler-dom": "3.0.11", "@vue/compiler-ssr": "3.0.11", "@vue/shared": "3.0.11", "estree-walker": "^2.0.1", "hash-sum": "2.0.0", "lru-cache": "5.1.1", "magic-string": "0.25.7", "merge-source-map": "^1.1.0", "postcss": "8.1.10", "postcss-modules": "4.0.0", "postcss-selector-parser": "6.0.4", "source-map": "^0.6.1"}, "devDependencies": {"@types/lru-cache": "^5.1.0", "pug": "^3.0.1", "sass": "^1.26.9"}}