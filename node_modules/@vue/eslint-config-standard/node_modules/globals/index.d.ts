// This file is autogenerated by scripts/generate-types.mjs
// Do NOT modify this file manually

type GlobalsAmd = {
	readonly 'define': false;
	readonly 'require': false;
}

type GlobalsApplescript = {
	readonly '$': false;
	readonly 'Application': false;
	readonly 'Automation': false;
	readonly 'console': false;
	readonly 'delay': false;
	readonly 'Library': false;
	readonly 'ObjC': false;
	readonly 'ObjectSpecifier': false;
	readonly 'Path': false;
	readonly 'Progress': false;
	readonly 'Ref': false;
}

type GlobalsAtomtest = {
	readonly 'advanceClock': false;
	readonly 'atom': false;
	readonly 'fakeClearInterval': false;
	readonly 'fakeClearTimeout': false;
	readonly 'fakeSetInterval': false;
	readonly 'fakeSetTimeout': false;
	readonly 'resetTimeouts': false;
	readonly 'waitsForPromise': false;
}

type GlobalsBrowser = {
	readonly 'AbortController': false;
	readonly 'AbortSignal': false;
	readonly 'AbsoluteOrientationSensor': false;
	readonly 'AbstractRange': false;
	readonly 'Accelerometer': false;
	readonly 'addEventListener': false;
	readonly 'ai': false;
	readonly 'AI': false;
	readonly 'AICreateMonitor': false;
	readonly 'AITextSession': false;
	readonly 'alert': false;
	readonly 'AnalyserNode': false;
	readonly 'Animation': false;
	readonly 'AnimationEffect': false;
	readonly 'AnimationEvent': false;
	readonly 'AnimationPlaybackEvent': false;
	readonly 'AnimationTimeline': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'Attr': false;
	readonly 'Audio': false;
	readonly 'AudioBuffer': false;
	readonly 'AudioBufferSourceNode': false;
	readonly 'AudioContext': false;
	readonly 'AudioData': false;
	readonly 'AudioDecoder': false;
	readonly 'AudioDestinationNode': false;
	readonly 'AudioEncoder': false;
	readonly 'AudioListener': false;
	readonly 'AudioNode': false;
	readonly 'AudioParam': false;
	readonly 'AudioParamMap': false;
	readonly 'AudioProcessingEvent': false;
	readonly 'AudioScheduledSourceNode': false;
	readonly 'AudioSinkInfo': false;
	readonly 'AudioWorklet': false;
	readonly 'AudioWorkletGlobalScope': false;
	readonly 'AudioWorkletNode': false;
	readonly 'AudioWorkletProcessor': false;
	readonly 'AuthenticatorAssertionResponse': false;
	readonly 'AuthenticatorAttestationResponse': false;
	readonly 'AuthenticatorResponse': false;
	readonly 'BackgroundFetchManager': false;
	readonly 'BackgroundFetchRecord': false;
	readonly 'BackgroundFetchRegistration': false;
	readonly 'BarcodeDetector': false;
	readonly 'BarProp': false;
	readonly 'BaseAudioContext': false;
	readonly 'BatteryManager': false;
	readonly 'BeforeUnloadEvent': false;
	readonly 'BiquadFilterNode': false;
	readonly 'Blob': false;
	readonly 'BlobEvent': false;
	readonly 'Bluetooth': false;
	readonly 'BluetoothCharacteristicProperties': false;
	readonly 'BluetoothDevice': false;
	readonly 'BluetoothRemoteGATTCharacteristic': false;
	readonly 'BluetoothRemoteGATTDescriptor': false;
	readonly 'BluetoothRemoteGATTServer': false;
	readonly 'BluetoothRemoteGATTService': false;
	readonly 'BluetoothUUID': false;
	readonly 'blur': false;
	readonly 'BroadcastChannel': false;
	readonly 'BrowserCaptureMediaStreamTrack': false;
	readonly 'btoa': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'Cache': false;
	readonly 'caches': false;
	readonly 'CacheStorage': false;
	readonly 'cancelAnimationFrame': false;
	readonly 'cancelIdleCallback': false;
	readonly 'CanvasCaptureMediaStream': false;
	readonly 'CanvasCaptureMediaStreamTrack': false;
	readonly 'CanvasGradient': false;
	readonly 'CanvasPattern': false;
	readonly 'CanvasRenderingContext2D': false;
	readonly 'CaptureController': false;
	readonly 'CaretPosition': false;
	readonly 'CDATASection': false;
	readonly 'ChannelMergerNode': false;
	readonly 'ChannelSplitterNode': false;
	readonly 'ChapterInformation': false;
	readonly 'CharacterBoundsUpdateEvent': false;
	readonly 'CharacterData': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'clientInformation': false;
	readonly 'Clipboard': false;
	readonly 'ClipboardEvent': false;
	readonly 'ClipboardItem': false;
	readonly 'close': false;
	readonly 'closed': false;
	readonly 'CloseEvent': false;
	readonly 'CloseWatcher': false;
	readonly 'CommandEvent': false;
	readonly 'Comment': false;
	readonly 'CompositionEvent': false;
	readonly 'CompressionStream': false;
	readonly 'confirm': false;
	readonly 'console': false;
	readonly 'ConstantSourceNode': false;
	readonly 'ContentVisibilityAutoStateChangeEvent': false;
	readonly 'ConvolverNode': false;
	readonly 'CookieChangeEvent': false;
	readonly 'CookieDeprecationLabel': false;
	readonly 'cookieStore': false;
	readonly 'CookieStore': false;
	readonly 'CookieStoreManager': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'createImageBitmap': false;
	readonly 'Credential': false;
	readonly 'credentialless': false;
	readonly 'CredentialsContainer': false;
	readonly 'CropTarget': false;
	readonly 'crossOriginIsolated': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CSPViolationReportBody': false;
	readonly 'CSS': false;
	readonly 'CSSAnimation': false;
	readonly 'CSSConditionRule': false;
	readonly 'CSSContainerRule': false;
	readonly 'CSSCounterStyleRule': false;
	readonly 'CSSFontFaceRule': false;
	readonly 'CSSFontFeatureValuesRule': false;
	readonly 'CSSFontPaletteValuesRule': false;
	readonly 'CSSGroupingRule': false;
	readonly 'CSSImageValue': false;
	readonly 'CSSImportRule': false;
	readonly 'CSSKeyframeRule': false;
	readonly 'CSSKeyframesRule': false;
	readonly 'CSSKeywordValue': false;
	readonly 'CSSLayerBlockRule': false;
	readonly 'CSSLayerStatementRule': false;
	readonly 'CSSMarginRule': false;
	readonly 'CSSMathClamp': false;
	readonly 'CSSMathInvert': false;
	readonly 'CSSMathMax': false;
	readonly 'CSSMathMin': false;
	readonly 'CSSMathNegate': false;
	readonly 'CSSMathProduct': false;
	readonly 'CSSMathSum': false;
	readonly 'CSSMathValue': false;
	readonly 'CSSMatrixComponent': false;
	readonly 'CSSMediaRule': false;
	readonly 'CSSNamespaceRule': false;
	readonly 'CSSNestedDeclarations': false;
	readonly 'CSSNumericArray': false;
	readonly 'CSSNumericValue': false;
	readonly 'CSSPageDescriptors': false;
	readonly 'CSSPageRule': false;
	readonly 'CSSPerspective': false;
	readonly 'CSSPositionTryDescriptors': false;
	readonly 'CSSPositionTryRule': false;
	readonly 'CSSPositionValue': false;
	readonly 'CSSPropertyRule': false;
	readonly 'CSSRotate': false;
	readonly 'CSSRule': false;
	readonly 'CSSRuleList': false;
	readonly 'CSSScale': false;
	readonly 'CSSScopeRule': false;
	readonly 'CSSSkew': false;
	readonly 'CSSSkewX': false;
	readonly 'CSSSkewY': false;
	readonly 'CSSStartingStyleRule': false;
	readonly 'CSSStyleDeclaration': false;
	readonly 'CSSStyleRule': false;
	readonly 'CSSStyleSheet': false;
	readonly 'CSSStyleValue': false;
	readonly 'CSSSupportsRule': false;
	readonly 'CSSTransformComponent': false;
	readonly 'CSSTransformValue': false;
	readonly 'CSSTransition': false;
	readonly 'CSSTranslate': false;
	readonly 'CSSUnitValue': false;
	readonly 'CSSUnparsedValue': false;
	readonly 'CSSVariableReferenceValue': false;
	readonly 'CSSViewTransitionRule': false;
	readonly 'currentFrame': false;
	readonly 'currentTime': false;
	readonly 'CustomElementRegistry': false;
	readonly 'customElements': false;
	readonly 'CustomEvent': false;
	readonly 'CustomStateSet': false;
	readonly 'DataTransfer': false;
	readonly 'DataTransferItem': false;
	readonly 'DataTransferItemList': false;
	readonly 'DecompressionStream': false;
	readonly 'DelayNode': false;
	readonly 'DelegatedInkTrailPresenter': false;
	readonly 'DeviceMotionEvent': false;
	readonly 'DeviceMotionEventAcceleration': false;
	readonly 'DeviceMotionEventRotationRate': false;
	readonly 'DeviceOrientationEvent': false;
	readonly 'devicePixelRatio': false;
	readonly 'DevicePosture': false;
	readonly 'dispatchEvent': false;
	readonly 'DisposableStack': false;
	readonly 'document': false;
	readonly 'Document': false;
	readonly 'DocumentFragment': false;
	readonly 'documentPictureInPicture': false;
	readonly 'DocumentPictureInPicture': false;
	readonly 'DocumentPictureInPictureEvent': false;
	readonly 'DocumentTimeline': false;
	readonly 'DocumentType': false;
	readonly 'DOMError': false;
	readonly 'DOMException': false;
	readonly 'DOMImplementation': false;
	readonly 'DOMMatrix': false;
	readonly 'DOMMatrixReadOnly': false;
	readonly 'DOMParser': false;
	readonly 'DOMPoint': false;
	readonly 'DOMPointReadOnly': false;
	readonly 'DOMQuad': false;
	readonly 'DOMRect': false;
	readonly 'DOMRectList': false;
	readonly 'DOMRectReadOnly': false;
	readonly 'DOMStringList': false;
	readonly 'DOMStringMap': false;
	readonly 'DOMTokenList': false;
	readonly 'DragEvent': false;
	readonly 'DynamicsCompressorNode': false;
	readonly 'EditContext': false;
	readonly 'Element': false;
	readonly 'ElementInternals': false;
	readonly 'EncodedAudioChunk': false;
	readonly 'EncodedVideoChunk': false;
	readonly 'ErrorEvent': false;
	readonly 'event': false;
	readonly 'Event': false;
	readonly 'EventCounts': false;
	readonly 'EventSource': false;
	readonly 'EventTarget': false;
	readonly 'external': false;
	readonly 'External': false;
	readonly 'EyeDropper': false;
	readonly 'FeaturePolicy': false;
	readonly 'FederatedCredential': false;
	readonly 'fence': false;
	readonly 'Fence': false;
	readonly 'FencedFrameConfig': false;
	readonly 'fetch': false;
	readonly 'fetchLater': false;
	readonly 'FetchLaterResult': false;
	readonly 'File': false;
	readonly 'FileList': false;
	readonly 'FileReader': false;
	readonly 'FileSystem': false;
	readonly 'FileSystemDirectoryEntry': false;
	readonly 'FileSystemDirectoryHandle': false;
	readonly 'FileSystemDirectoryReader': false;
	readonly 'FileSystemEntry': false;
	readonly 'FileSystemFileEntry': false;
	readonly 'FileSystemFileHandle': false;
	readonly 'FileSystemHandle': false;
	readonly 'FileSystemObserver': false;
	readonly 'FileSystemWritableFileStream': false;
	readonly 'find': false;
	readonly 'focus': false;
	readonly 'FocusEvent': false;
	readonly 'FontData': false;
	readonly 'FontFace': false;
	readonly 'FontFaceSet': false;
	readonly 'FontFaceSetLoadEvent': false;
	readonly 'FormData': false;
	readonly 'FormDataEvent': false;
	readonly 'FragmentDirective': false;
	readonly 'frameElement': false;
	readonly 'frames': false;
	readonly 'GainNode': false;
	readonly 'Gamepad': false;
	readonly 'GamepadAxisMoveEvent': false;
	readonly 'GamepadButton': false;
	readonly 'GamepadButtonEvent': false;
	readonly 'GamepadEvent': false;
	readonly 'GamepadHapticActuator': false;
	readonly 'GamepadPose': false;
	readonly 'Geolocation': false;
	readonly 'GeolocationCoordinates': false;
	readonly 'GeolocationPosition': false;
	readonly 'GeolocationPositionError': false;
	readonly 'getComputedStyle': false;
	readonly 'getScreenDetails': false;
	readonly 'getSelection': false;
	readonly 'GPU': false;
	readonly 'GPUAdapter': false;
	readonly 'GPUAdapterInfo': false;
	readonly 'GPUBindGroup': false;
	readonly 'GPUBindGroupLayout': false;
	readonly 'GPUBuffer': false;
	readonly 'GPUBufferUsage': false;
	readonly 'GPUCanvasContext': false;
	readonly 'GPUColorWrite': false;
	readonly 'GPUCommandBuffer': false;
	readonly 'GPUCommandEncoder': false;
	readonly 'GPUCompilationInfo': false;
	readonly 'GPUCompilationMessage': false;
	readonly 'GPUComputePassEncoder': false;
	readonly 'GPUComputePipeline': false;
	readonly 'GPUDevice': false;
	readonly 'GPUDeviceLostInfo': false;
	readonly 'GPUError': false;
	readonly 'GPUExternalTexture': false;
	readonly 'GPUInternalError': false;
	readonly 'GPUMapMode': false;
	readonly 'GPUOutOfMemoryError': false;
	readonly 'GPUPipelineError': false;
	readonly 'GPUPipelineLayout': false;
	readonly 'GPUQuerySet': false;
	readonly 'GPUQueue': false;
	readonly 'GPURenderBundle': false;
	readonly 'GPURenderBundleEncoder': false;
	readonly 'GPURenderPassEncoder': false;
	readonly 'GPURenderPipeline': false;
	readonly 'GPUSampler': false;
	readonly 'GPUShaderModule': false;
	readonly 'GPUShaderStage': false;
	readonly 'GPUSupportedFeatures': false;
	readonly 'GPUSupportedLimits': false;
	readonly 'GPUTexture': false;
	readonly 'GPUTextureUsage': false;
	readonly 'GPUTextureView': false;
	readonly 'GPUUncapturedErrorEvent': false;
	readonly 'GPUValidationError': false;
	readonly 'GravitySensor': false;
	readonly 'Gyroscope': false;
	readonly 'HashChangeEvent': false;
	readonly 'Headers': false;
	readonly 'HID': false;
	readonly 'HIDConnectionEvent': false;
	readonly 'HIDDevice': false;
	readonly 'HIDInputReportEvent': false;
	readonly 'Highlight': false;
	readonly 'HighlightRegistry': false;
	readonly 'history': false;
	readonly 'History': false;
	readonly 'HTMLAllCollection': false;
	readonly 'HTMLAnchorElement': false;
	readonly 'HTMLAreaElement': false;
	readonly 'HTMLAudioElement': false;
	readonly 'HTMLBaseElement': false;
	readonly 'HTMLBodyElement': false;
	readonly 'HTMLBRElement': false;
	readonly 'HTMLButtonElement': false;
	readonly 'HTMLCanvasElement': false;
	readonly 'HTMLCollection': false;
	readonly 'HTMLDataElement': false;
	readonly 'HTMLDataListElement': false;
	readonly 'HTMLDetailsElement': false;
	readonly 'HTMLDialogElement': false;
	readonly 'HTMLDirectoryElement': false;
	readonly 'HTMLDivElement': false;
	readonly 'HTMLDListElement': false;
	readonly 'HTMLDocument': false;
	readonly 'HTMLElement': false;
	readonly 'HTMLEmbedElement': false;
	readonly 'HTMLFencedFrameElement': false;
	readonly 'HTMLFieldSetElement': false;
	readonly 'HTMLFontElement': false;
	readonly 'HTMLFormControlsCollection': false;
	readonly 'HTMLFormElement': false;
	readonly 'HTMLFrameElement': false;
	readonly 'HTMLFrameSetElement': false;
	readonly 'HTMLHeadElement': false;
	readonly 'HTMLHeadingElement': false;
	readonly 'HTMLHRElement': false;
	readonly 'HTMLHtmlElement': false;
	readonly 'HTMLIFrameElement': false;
	readonly 'HTMLImageElement': false;
	readonly 'HTMLInputElement': false;
	readonly 'HTMLLabelElement': false;
	readonly 'HTMLLegendElement': false;
	readonly 'HTMLLIElement': false;
	readonly 'HTMLLinkElement': false;
	readonly 'HTMLMapElement': false;
	readonly 'HTMLMarqueeElement': false;
	readonly 'HTMLMediaElement': false;
	readonly 'HTMLMenuElement': false;
	readonly 'HTMLMetaElement': false;
	readonly 'HTMLMeterElement': false;
	readonly 'HTMLModElement': false;
	readonly 'HTMLObjectElement': false;
	readonly 'HTMLOListElement': false;
	readonly 'HTMLOptGroupElement': false;
	readonly 'HTMLOptionElement': false;
	readonly 'HTMLOptionsCollection': false;
	readonly 'HTMLOutputElement': false;
	readonly 'HTMLParagraphElement': false;
	readonly 'HTMLParamElement': false;
	readonly 'HTMLPictureElement': false;
	readonly 'HTMLPreElement': false;
	readonly 'HTMLProgressElement': false;
	readonly 'HTMLQuoteElement': false;
	readonly 'HTMLScriptElement': false;
	readonly 'HTMLSelectedContentElement': false;
	readonly 'HTMLSelectElement': false;
	readonly 'HTMLSlotElement': false;
	readonly 'HTMLSourceElement': false;
	readonly 'HTMLSpanElement': false;
	readonly 'HTMLStyleElement': false;
	readonly 'HTMLTableCaptionElement': false;
	readonly 'HTMLTableCellElement': false;
	readonly 'HTMLTableColElement': false;
	readonly 'HTMLTableElement': false;
	readonly 'HTMLTableRowElement': false;
	readonly 'HTMLTableSectionElement': false;
	readonly 'HTMLTemplateElement': false;
	readonly 'HTMLTextAreaElement': false;
	readonly 'HTMLTimeElement': false;
	readonly 'HTMLTitleElement': false;
	readonly 'HTMLTrackElement': false;
	readonly 'HTMLUListElement': false;
	readonly 'HTMLUnknownElement': false;
	readonly 'HTMLVideoElement': false;
	readonly 'IDBCursor': false;
	readonly 'IDBCursorWithValue': false;
	readonly 'IDBDatabase': false;
	readonly 'IDBFactory': false;
	readonly 'IDBIndex': false;
	readonly 'IDBKeyRange': false;
	readonly 'IDBObjectStore': false;
	readonly 'IDBOpenDBRequest': false;
	readonly 'IDBRequest': false;
	readonly 'IDBTransaction': false;
	readonly 'IDBVersionChangeEvent': false;
	readonly 'IdentityCredential': false;
	readonly 'IdentityCredentialError': false;
	readonly 'IdentityProvider': false;
	readonly 'IdleDeadline': false;
	readonly 'IdleDetector': false;
	readonly 'IIRFilterNode': false;
	readonly 'Image': false;
	readonly 'ImageBitmap': false;
	readonly 'ImageBitmapRenderingContext': false;
	readonly 'ImageCapture': false;
	readonly 'ImageData': false;
	readonly 'ImageDecoder': false;
	readonly 'ImageTrack': false;
	readonly 'ImageTrackList': false;
	readonly 'indexedDB': false;
	readonly 'Ink': false;
	readonly 'innerHeight': false;
	readonly 'innerWidth': false;
	readonly 'InputDeviceCapabilities': false;
	readonly 'InputDeviceInfo': false;
	readonly 'InputEvent': false;
	readonly 'IntersectionObserver': false;
	readonly 'IntersectionObserverEntry': false;
	readonly 'isSecureContext': false;
	readonly 'Keyboard': false;
	readonly 'KeyboardEvent': false;
	readonly 'KeyboardLayoutMap': false;
	readonly 'KeyframeEffect': false;
	readonly 'LanguageDetector': false;
	readonly 'LargestContentfulPaint': false;
	readonly 'LaunchParams': false;
	readonly 'launchQueue': false;
	readonly 'LaunchQueue': false;
	readonly 'LayoutShift': false;
	readonly 'LayoutShiftAttribution': false;
	readonly 'length': false;
	readonly 'LinearAccelerationSensor': false;
	readonly 'localStorage': false;
	readonly 'location': true;
	readonly 'Location': false;
	readonly 'locationbar': false;
	readonly 'Lock': false;
	readonly 'LockManager': false;
	readonly 'matchMedia': false;
	readonly 'MathMLElement': false;
	readonly 'MediaCapabilities': false;
	readonly 'MediaCapabilitiesInfo': false;
	readonly 'MediaDeviceInfo': false;
	readonly 'MediaDevices': false;
	readonly 'MediaElementAudioSourceNode': false;
	readonly 'MediaEncryptedEvent': false;
	readonly 'MediaError': false;
	readonly 'MediaKeyError': false;
	readonly 'MediaKeyMessageEvent': false;
	readonly 'MediaKeys': false;
	readonly 'MediaKeySession': false;
	readonly 'MediaKeyStatusMap': false;
	readonly 'MediaKeySystemAccess': false;
	readonly 'MediaList': false;
	readonly 'MediaMetadata': false;
	readonly 'MediaQueryList': false;
	readonly 'MediaQueryListEvent': false;
	readonly 'MediaRecorder': false;
	readonly 'MediaRecorderErrorEvent': false;
	readonly 'MediaSession': false;
	readonly 'MediaSource': false;
	readonly 'MediaSourceHandle': false;
	readonly 'MediaStream': false;
	readonly 'MediaStreamAudioDestinationNode': false;
	readonly 'MediaStreamAudioSourceNode': false;
	readonly 'MediaStreamEvent': false;
	readonly 'MediaStreamTrack': false;
	readonly 'MediaStreamTrackAudioSourceNode': false;
	readonly 'MediaStreamTrackAudioStats': false;
	readonly 'MediaStreamTrackEvent': false;
	readonly 'MediaStreamTrackGenerator': false;
	readonly 'MediaStreamTrackProcessor': false;
	readonly 'MediaStreamTrackVideoStats': false;
	readonly 'menubar': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'MIDIAccess': false;
	readonly 'MIDIConnectionEvent': false;
	readonly 'MIDIInput': false;
	readonly 'MIDIInputMap': false;
	readonly 'MIDIMessageEvent': false;
	readonly 'MIDIOutput': false;
	readonly 'MIDIOutputMap': false;
	readonly 'MIDIPort': false;
	readonly 'MimeType': false;
	readonly 'MimeTypeArray': false;
	readonly 'model': false;
	readonly 'ModelGenericSession': false;
	readonly 'ModelManager': false;
	readonly 'MouseEvent': false;
	readonly 'moveBy': false;
	readonly 'moveTo': false;
	readonly 'MutationEvent': false;
	readonly 'MutationObserver': false;
	readonly 'MutationRecord': false;
	readonly 'name': false;
	readonly 'NamedNodeMap': false;
	readonly 'NavigateEvent': false;
	readonly 'navigation': false;
	readonly 'Navigation': false;
	readonly 'NavigationActivation': false;
	readonly 'NavigationCurrentEntryChangeEvent': false;
	readonly 'NavigationDestination': false;
	readonly 'NavigationHistoryEntry': false;
	readonly 'NavigationPreloadManager': false;
	readonly 'NavigationTransition': false;
	readonly 'navigator': false;
	readonly 'Navigator': false;
	readonly 'NavigatorLogin': false;
	readonly 'NavigatorManagedData': false;
	readonly 'NavigatorUAData': false;
	readonly 'NetworkInformation': false;
	readonly 'Node': false;
	readonly 'NodeFilter': false;
	readonly 'NodeIterator': false;
	readonly 'NodeList': false;
	readonly 'Notification': false;
	readonly 'NotifyPaintEvent': false;
	readonly 'NotRestoredReasonDetails': false;
	readonly 'NotRestoredReasons': false;
	readonly 'Observable': false;
	readonly 'OfflineAudioCompletionEvent': false;
	readonly 'OfflineAudioContext': false;
	readonly 'offscreenBuffering': false;
	readonly 'OffscreenCanvas': false;
	readonly 'OffscreenCanvasRenderingContext2D': false;
	readonly 'onabort': true;
	readonly 'onafterprint': true;
	readonly 'onanimationcancel': true;
	readonly 'onanimationend': true;
	readonly 'onanimationiteration': true;
	readonly 'onanimationstart': true;
	readonly 'onappinstalled': true;
	readonly 'onauxclick': true;
	readonly 'onbeforeinput': true;
	readonly 'onbeforeinstallprompt': true;
	readonly 'onbeforematch': true;
	readonly 'onbeforeprint': true;
	readonly 'onbeforetoggle': true;
	readonly 'onbeforeunload': true;
	readonly 'onbeforexrselect': true;
	readonly 'onblur': true;
	readonly 'oncancel': true;
	readonly 'oncanplay': true;
	readonly 'oncanplaythrough': true;
	readonly 'onchange': true;
	readonly 'onclick': true;
	readonly 'onclose': true;
	readonly 'oncommand': true;
	readonly 'oncontentvisibilityautostatechange': true;
	readonly 'oncontextlost': true;
	readonly 'oncontextmenu': true;
	readonly 'oncontextrestored': true;
	readonly 'oncopy': true;
	readonly 'oncuechange': true;
	readonly 'oncut': true;
	readonly 'ondblclick': true;
	readonly 'ondevicemotion': true;
	readonly 'ondeviceorientation': true;
	readonly 'ondeviceorientationabsolute': true;
	readonly 'ondrag': true;
	readonly 'ondragend': true;
	readonly 'ondragenter': true;
	readonly 'ondragleave': true;
	readonly 'ondragover': true;
	readonly 'ondragstart': true;
	readonly 'ondrop': true;
	readonly 'ondurationchange': true;
	readonly 'onemptied': true;
	readonly 'onended': true;
	readonly 'onerror': true;
	readonly 'onfocus': true;
	readonly 'onformdata': true;
	readonly 'ongamepadconnected': true;
	readonly 'ongamepaddisconnected': true;
	readonly 'ongotpointercapture': true;
	readonly 'onhashchange': true;
	readonly 'oninput': true;
	readonly 'oninvalid': true;
	readonly 'onkeydown': true;
	readonly 'onkeypress': true;
	readonly 'onkeyup': true;
	readonly 'onlanguagechange': true;
	readonly 'onload': true;
	readonly 'onloadeddata': true;
	readonly 'onloadedmetadata': true;
	readonly 'onloadstart': true;
	readonly 'onlostpointercapture': true;
	readonly 'onmessage': true;
	readonly 'onmessageerror': true;
	readonly 'onmousedown': true;
	readonly 'onmouseenter': true;
	readonly 'onmouseleave': true;
	readonly 'onmousemove': true;
	readonly 'onmouseout': true;
	readonly 'onmouseover': true;
	readonly 'onmouseup': true;
	readonly 'onmousewheel': true;
	readonly 'onoffline': true;
	readonly 'ononline': true;
	readonly 'onpagehide': true;
	readonly 'onpagereveal': true;
	readonly 'onpageshow': true;
	readonly 'onpageswap': true;
	readonly 'onpaste': true;
	readonly 'onpause': true;
	readonly 'onplay': true;
	readonly 'onplaying': true;
	readonly 'onpointercancel': true;
	readonly 'onpointerdown': true;
	readonly 'onpointerenter': true;
	readonly 'onpointerleave': true;
	readonly 'onpointermove': true;
	readonly 'onpointerout': true;
	readonly 'onpointerover': true;
	readonly 'onpointerrawupdate': true;
	readonly 'onpointerup': true;
	readonly 'onpopstate': true;
	readonly 'onprogress': true;
	readonly 'onratechange': true;
	readonly 'onrejectionhandled': true;
	readonly 'onreset': true;
	readonly 'onresize': true;
	readonly 'onscroll': true;
	readonly 'onscrollend': true;
	readonly 'onscrollsnapchange': true;
	readonly 'onscrollsnapchanging': true;
	readonly 'onsearch': true;
	readonly 'onsecuritypolicyviolation': true;
	readonly 'onseeked': true;
	readonly 'onseeking': true;
	readonly 'onselect': true;
	readonly 'onselectionchange': true;
	readonly 'onselectstart': true;
	readonly 'onslotchange': true;
	readonly 'onstalled': true;
	readonly 'onstorage': true;
	readonly 'onsubmit': true;
	readonly 'onsuspend': true;
	readonly 'ontimeupdate': true;
	readonly 'ontoggle': true;
	readonly 'ontransitioncancel': true;
	readonly 'ontransitionend': true;
	readonly 'ontransitionrun': true;
	readonly 'ontransitionstart': true;
	readonly 'onunhandledrejection': true;
	readonly 'onunload': true;
	readonly 'onvolumechange': true;
	readonly 'onwaiting': true;
	readonly 'onwheel': true;
	readonly 'open': false;
	readonly 'opener': false;
	readonly 'Option': false;
	readonly 'OrientationSensor': false;
	readonly 'origin': false;
	readonly 'originAgentCluster': false;
	readonly 'OscillatorNode': false;
	readonly 'OTPCredential': false;
	readonly 'outerHeight': false;
	readonly 'outerWidth': false;
	readonly 'OverconstrainedError': false;
	readonly 'PageRevealEvent': false;
	readonly 'PageSwapEvent': false;
	readonly 'PageTransitionEvent': false;
	readonly 'pageXOffset': false;
	readonly 'pageYOffset': false;
	readonly 'PannerNode': false;
	readonly 'parent': false;
	readonly 'PasswordCredential': false;
	readonly 'Path2D': false;
	readonly 'PaymentAddress': false;
	readonly 'PaymentManager': false;
	readonly 'PaymentMethodChangeEvent': false;
	readonly 'PaymentRequest': false;
	readonly 'PaymentRequestUpdateEvent': false;
	readonly 'PaymentResponse': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceElementTiming': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceEventTiming': false;
	readonly 'PerformanceLongAnimationFrameTiming': false;
	readonly 'PerformanceLongTaskTiming': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceNavigation': false;
	readonly 'PerformanceNavigationTiming': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformancePaintTiming': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'PerformanceScriptTiming': false;
	readonly 'PerformanceServerTiming': false;
	readonly 'PerformanceTiming': false;
	readonly 'PeriodicSyncManager': false;
	readonly 'PeriodicWave': false;
	readonly 'Permissions': false;
	readonly 'PermissionStatus': false;
	readonly 'PERSISTENT': false;
	readonly 'personalbar': false;
	readonly 'PictureInPictureEvent': false;
	readonly 'PictureInPictureWindow': false;
	readonly 'Plugin': false;
	readonly 'PluginArray': false;
	readonly 'PointerEvent': false;
	readonly 'PopStateEvent': false;
	readonly 'postMessage': false;
	readonly 'Presentation': false;
	readonly 'PresentationAvailability': false;
	readonly 'PresentationConnection': false;
	readonly 'PresentationConnectionAvailableEvent': false;
	readonly 'PresentationConnectionCloseEvent': false;
	readonly 'PresentationConnectionList': false;
	readonly 'PresentationReceiver': false;
	readonly 'PresentationRequest': false;
	readonly 'PressureObserver': false;
	readonly 'PressureRecord': false;
	readonly 'print': false;
	readonly 'ProcessingInstruction': false;
	readonly 'Profiler': false;
	readonly 'ProgressEvent': false;
	readonly 'PromiseRejectionEvent': false;
	readonly 'prompt': false;
	readonly 'ProtectedAudience': false;
	readonly 'PublicKeyCredential': false;
	readonly 'PushManager': false;
	readonly 'PushSubscription': false;
	readonly 'PushSubscriptionOptions': false;
	readonly 'queryLocalFonts': false;
	readonly 'queueMicrotask': false;
	readonly 'RadioNodeList': false;
	readonly 'Range': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'registerProcessor': false;
	readonly 'RelativeOrientationSensor': false;
	readonly 'RemotePlayback': false;
	readonly 'removeEventListener': false;
	readonly 'ReportBody': false;
	readonly 'reportError': false;
	readonly 'ReportingObserver': false;
	readonly 'Request': false;
	readonly 'requestAnimationFrame': false;
	readonly 'requestIdleCallback': false;
	readonly 'resizeBy': false;
	readonly 'ResizeObserver': false;
	readonly 'ResizeObserverEntry': false;
	readonly 'ResizeObserverSize': false;
	readonly 'resizeTo': false;
	readonly 'Response': false;
	readonly 'RestrictionTarget': false;
	readonly 'RTCCertificate': false;
	readonly 'RTCDataChannel': false;
	readonly 'RTCDataChannelEvent': false;
	readonly 'RTCDtlsTransport': false;
	readonly 'RTCDTMFSender': false;
	readonly 'RTCDTMFToneChangeEvent': false;
	readonly 'RTCEncodedAudioFrame': false;
	readonly 'RTCEncodedVideoFrame': false;
	readonly 'RTCError': false;
	readonly 'RTCErrorEvent': false;
	readonly 'RTCIceCandidate': false;
	readonly 'RTCIceTransport': false;
	readonly 'RTCPeerConnection': false;
	readonly 'RTCPeerConnectionIceErrorEvent': false;
	readonly 'RTCPeerConnectionIceEvent': false;
	readonly 'RTCRtpReceiver': false;
	readonly 'RTCRtpScriptTransform': false;
	readonly 'RTCRtpSender': false;
	readonly 'RTCRtpTransceiver': false;
	readonly 'RTCSctpTransport': false;
	readonly 'RTCSessionDescription': false;
	readonly 'RTCStatsReport': false;
	readonly 'RTCTrackEvent': false;
	readonly 'sampleRate': false;
	readonly 'scheduler': false;
	readonly 'Scheduler': false;
	readonly 'Scheduling': false;
	readonly 'screen': false;
	readonly 'Screen': false;
	readonly 'ScreenDetailed': false;
	readonly 'ScreenDetails': false;
	readonly 'screenLeft': false;
	readonly 'ScreenOrientation': false;
	readonly 'screenTop': false;
	readonly 'screenX': false;
	readonly 'screenY': false;
	readonly 'ScriptProcessorNode': false;
	readonly 'scroll': false;
	readonly 'scrollbars': false;
	readonly 'scrollBy': false;
	readonly 'ScrollTimeline': false;
	readonly 'scrollTo': false;
	readonly 'scrollX': false;
	readonly 'scrollY': false;
	readonly 'SecurityPolicyViolationEvent': false;
	readonly 'Selection': false;
	readonly 'self': false;
	readonly 'Sensor': false;
	readonly 'SensorErrorEvent': false;
	readonly 'Serial': false;
	readonly 'SerialPort': false;
	readonly 'ServiceWorker': false;
	readonly 'ServiceWorkerContainer': false;
	readonly 'ServiceWorkerRegistration': false;
	readonly 'sessionStorage': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'ShadowRoot': false;
	readonly 'sharedStorage': false;
	readonly 'SharedStorage': false;
	readonly 'SharedStorageAppendMethod': false;
	readonly 'SharedStorageClearMethod': false;
	readonly 'SharedStorageDeleteMethod': false;
	readonly 'SharedStorageModifierMethod': false;
	readonly 'SharedStorageSetMethod': false;
	readonly 'SharedStorageWorklet': false;
	readonly 'SharedWorker': false;
	readonly 'showDirectoryPicker': false;
	readonly 'showOpenFilePicker': false;
	readonly 'showSaveFilePicker': false;
	readonly 'SnapEvent': false;
	readonly 'SourceBuffer': false;
	readonly 'SourceBufferList': false;
	readonly 'speechSynthesis': false;
	readonly 'SpeechSynthesis': false;
	readonly 'SpeechSynthesisErrorEvent': false;
	readonly 'SpeechSynthesisEvent': false;
	readonly 'SpeechSynthesisUtterance': false;
	readonly 'SpeechSynthesisVoice': false;
	readonly 'StaticRange': false;
	readonly 'status': false;
	readonly 'statusbar': false;
	readonly 'StereoPannerNode': false;
	readonly 'stop': false;
	readonly 'Storage': false;
	readonly 'StorageBucket': false;
	readonly 'StorageBucketManager': false;
	readonly 'StorageEvent': false;
	readonly 'StorageManager': false;
	readonly 'structuredClone': false;
	readonly 'styleMedia': false;
	readonly 'StylePropertyMap': false;
	readonly 'StylePropertyMapReadOnly': false;
	readonly 'StyleSheet': false;
	readonly 'StyleSheetList': false;
	readonly 'SubmitEvent': false;
	readonly 'Subscriber': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'SVGAElement': false;
	readonly 'SVGAngle': false;
	readonly 'SVGAnimatedAngle': false;
	readonly 'SVGAnimatedBoolean': false;
	readonly 'SVGAnimatedEnumeration': false;
	readonly 'SVGAnimatedInteger': false;
	readonly 'SVGAnimatedLength': false;
	readonly 'SVGAnimatedLengthList': false;
	readonly 'SVGAnimatedNumber': false;
	readonly 'SVGAnimatedNumberList': false;
	readonly 'SVGAnimatedPreserveAspectRatio': false;
	readonly 'SVGAnimatedRect': false;
	readonly 'SVGAnimatedString': false;
	readonly 'SVGAnimatedTransformList': false;
	readonly 'SVGAnimateElement': false;
	readonly 'SVGAnimateMotionElement': false;
	readonly 'SVGAnimateTransformElement': false;
	readonly 'SVGAnimationElement': false;
	readonly 'SVGCircleElement': false;
	readonly 'SVGClipPathElement': false;
	readonly 'SVGComponentTransferFunctionElement': false;
	readonly 'SVGDefsElement': false;
	readonly 'SVGDescElement': false;
	readonly 'SVGElement': false;
	readonly 'SVGEllipseElement': false;
	readonly 'SVGFEBlendElement': false;
	readonly 'SVGFEColorMatrixElement': false;
	readonly 'SVGFEComponentTransferElement': false;
	readonly 'SVGFECompositeElement': false;
	readonly 'SVGFEConvolveMatrixElement': false;
	readonly 'SVGFEDiffuseLightingElement': false;
	readonly 'SVGFEDisplacementMapElement': false;
	readonly 'SVGFEDistantLightElement': false;
	readonly 'SVGFEDropShadowElement': false;
	readonly 'SVGFEFloodElement': false;
	readonly 'SVGFEFuncAElement': false;
	readonly 'SVGFEFuncBElement': false;
	readonly 'SVGFEFuncGElement': false;
	readonly 'SVGFEFuncRElement': false;
	readonly 'SVGFEGaussianBlurElement': false;
	readonly 'SVGFEImageElement': false;
	readonly 'SVGFEMergeElement': false;
	readonly 'SVGFEMergeNodeElement': false;
	readonly 'SVGFEMorphologyElement': false;
	readonly 'SVGFEOffsetElement': false;
	readonly 'SVGFEPointLightElement': false;
	readonly 'SVGFESpecularLightingElement': false;
	readonly 'SVGFESpotLightElement': false;
	readonly 'SVGFETileElement': false;
	readonly 'SVGFETurbulenceElement': false;
	readonly 'SVGFilterElement': false;
	readonly 'SVGForeignObjectElement': false;
	readonly 'SVGGElement': false;
	readonly 'SVGGeometryElement': false;
	readonly 'SVGGradientElement': false;
	readonly 'SVGGraphicsElement': false;
	readonly 'SVGImageElement': false;
	readonly 'SVGLength': false;
	readonly 'SVGLengthList': false;
	readonly 'SVGLinearGradientElement': false;
	readonly 'SVGLineElement': false;
	readonly 'SVGMarkerElement': false;
	readonly 'SVGMaskElement': false;
	readonly 'SVGMatrix': false;
	readonly 'SVGMetadataElement': false;
	readonly 'SVGMPathElement': false;
	readonly 'SVGNumber': false;
	readonly 'SVGNumberList': false;
	readonly 'SVGPathElement': false;
	readonly 'SVGPatternElement': false;
	readonly 'SVGPoint': false;
	readonly 'SVGPointList': false;
	readonly 'SVGPolygonElement': false;
	readonly 'SVGPolylineElement': false;
	readonly 'SVGPreserveAspectRatio': false;
	readonly 'SVGRadialGradientElement': false;
	readonly 'SVGRect': false;
	readonly 'SVGRectElement': false;
	readonly 'SVGScriptElement': false;
	readonly 'SVGSetElement': false;
	readonly 'SVGStopElement': false;
	readonly 'SVGStringList': false;
	readonly 'SVGStyleElement': false;
	readonly 'SVGSVGElement': false;
	readonly 'SVGSwitchElement': false;
	readonly 'SVGSymbolElement': false;
	readonly 'SVGTextContentElement': false;
	readonly 'SVGTextElement': false;
	readonly 'SVGTextPathElement': false;
	readonly 'SVGTextPositioningElement': false;
	readonly 'SVGTitleElement': false;
	readonly 'SVGTransform': false;
	readonly 'SVGTransformList': false;
	readonly 'SVGTSpanElement': false;
	readonly 'SVGUnitTypes': false;
	readonly 'SVGUseElement': false;
	readonly 'SVGViewElement': false;
	readonly 'SyncManager': false;
	readonly 'TaskAttributionTiming': false;
	readonly 'TaskController': false;
	readonly 'TaskPriorityChangeEvent': false;
	readonly 'TaskSignal': false;
	readonly 'TEMPORARY': false;
	readonly 'Text': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TextEvent': false;
	readonly 'TextFormat': false;
	readonly 'TextFormatUpdateEvent': false;
	readonly 'TextMetrics': false;
	readonly 'TextTrack': false;
	readonly 'TextTrackCue': false;
	readonly 'TextTrackCueList': false;
	readonly 'TextTrackList': false;
	readonly 'TextUpdateEvent': false;
	readonly 'TimeEvent': false;
	readonly 'TimeRanges': false;
	readonly 'ToggleEvent': false;
	readonly 'toolbar': false;
	readonly 'top': false;
	readonly 'Touch': false;
	readonly 'TouchEvent': false;
	readonly 'TouchList': false;
	readonly 'TrackEvent': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'TransitionEvent': false;
	readonly 'TreeWalker': false;
	readonly 'TrustedHTML': false;
	readonly 'TrustedScript': false;
	readonly 'TrustedScriptURL': false;
	readonly 'TrustedTypePolicy': false;
	readonly 'TrustedTypePolicyFactory': false;
	readonly 'trustedTypes': false;
	readonly 'UIEvent': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'USB': false;
	readonly 'USBAlternateInterface': false;
	readonly 'USBConfiguration': false;
	readonly 'USBConnectionEvent': false;
	readonly 'USBDevice': false;
	readonly 'USBEndpoint': false;
	readonly 'USBInterface': false;
	readonly 'USBInTransferResult': false;
	readonly 'USBIsochronousInTransferPacket': false;
	readonly 'USBIsochronousInTransferResult': false;
	readonly 'USBIsochronousOutTransferPacket': false;
	readonly 'USBIsochronousOutTransferResult': false;
	readonly 'USBOutTransferResult': false;
	readonly 'UserActivation': false;
	readonly 'ValidityState': false;
	readonly 'VideoColorSpace': false;
	readonly 'VideoDecoder': false;
	readonly 'VideoEncoder': false;
	readonly 'VideoFrame': false;
	readonly 'VideoPlaybackQuality': false;
	readonly 'ViewTimeline': false;
	readonly 'ViewTransition': false;
	readonly 'ViewTransitionTypeSet': false;
	readonly 'VirtualKeyboard': false;
	readonly 'VirtualKeyboardGeometryChangeEvent': false;
	readonly 'VisibilityStateEntry': false;
	readonly 'visualViewport': false;
	readonly 'VisualViewport': false;
	readonly 'VTTCue': false;
	readonly 'VTTRegion': false;
	readonly 'WakeLock': false;
	readonly 'WakeLockSentinel': false;
	readonly 'WaveShaperNode': false;
	readonly 'WebAssembly': false;
	readonly 'WebGL2RenderingContext': false;
	readonly 'WebGLActiveInfo': false;
	readonly 'WebGLBuffer': false;
	readonly 'WebGLContextEvent': false;
	readonly 'WebGLFramebuffer': false;
	readonly 'WebGLObject': false;
	readonly 'WebGLProgram': false;
	readonly 'WebGLQuery': false;
	readonly 'WebGLRenderbuffer': false;
	readonly 'WebGLRenderingContext': false;
	readonly 'WebGLSampler': false;
	readonly 'WebGLShader': false;
	readonly 'WebGLShaderPrecisionFormat': false;
	readonly 'WebGLSync': false;
	readonly 'WebGLTexture': false;
	readonly 'WebGLTransformFeedback': false;
	readonly 'WebGLUniformLocation': false;
	readonly 'WebGLVertexArrayObject': false;
	readonly 'WebSocket': false;
	readonly 'WebSocketError': false;
	readonly 'WebSocketStream': false;
	readonly 'WebTransport': false;
	readonly 'WebTransportBidirectionalStream': false;
	readonly 'WebTransportDatagramDuplexStream': false;
	readonly 'WebTransportError': false;
	readonly 'WebTransportReceiveStream': false;
	readonly 'WebTransportSendStream': false;
	readonly 'WGSLLanguageFeatures': false;
	readonly 'WheelEvent': false;
	readonly 'when': false;
	readonly 'window': false;
	readonly 'Window': false;
	readonly 'WindowControlsOverlay': false;
	readonly 'WindowControlsOverlayGeometryChangeEvent': false;
	readonly 'Worker': false;
	readonly 'Worklet': false;
	readonly 'WorkletGlobalScope': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
	readonly 'XMLDocument': false;
	readonly 'XMLHttpRequest': false;
	readonly 'XMLHttpRequestEventTarget': false;
	readonly 'XMLHttpRequestUpload': false;
	readonly 'XMLSerializer': false;
	readonly 'XPathEvaluator': false;
	readonly 'XPathExpression': false;
	readonly 'XPathResult': false;
	readonly 'XRAnchor': false;
	readonly 'XRAnchorSet': false;
	readonly 'XRBoundedReferenceSpace': false;
	readonly 'XRCamera': false;
	readonly 'XRCPUDepthInformation': false;
	readonly 'XRDepthInformation': false;
	readonly 'XRDOMOverlayState': false;
	readonly 'XRFrame': false;
	readonly 'XRHand': false;
	readonly 'XRHitTestResult': false;
	readonly 'XRHitTestSource': false;
	readonly 'XRInputSource': false;
	readonly 'XRInputSourceArray': false;
	readonly 'XRInputSourceEvent': false;
	readonly 'XRInputSourcesChangeEvent': false;
	readonly 'XRJointPose': false;
	readonly 'XRJointSpace': false;
	readonly 'XRLayer': false;
	readonly 'XRLightEstimate': false;
	readonly 'XRLightProbe': false;
	readonly 'XRPose': false;
	readonly 'XRRay': false;
	readonly 'XRReferenceSpace': false;
	readonly 'XRReferenceSpaceEvent': false;
	readonly 'XRRenderState': false;
	readonly 'XRRigidTransform': false;
	readonly 'XRSession': false;
	readonly 'XRSessionEvent': false;
	readonly 'XRSpace': false;
	readonly 'XRSystem': false;
	readonly 'XRTransientInputHitTestResult': false;
	readonly 'XRTransientInputHitTestSource': false;
	readonly 'XRView': false;
	readonly 'XRViewerPose': false;
	readonly 'XRViewport': false;
	readonly 'XRWebGLBinding': false;
	readonly 'XRWebGLDepthInformation': false;
	readonly 'XRWebGLLayer': false;
	readonly 'XSLTProcessor': false;
}

type GlobalsBuiltin = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float16Array': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'Iterator': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsChai = {
	readonly 'assert': true;
	readonly 'expect': true;
	readonly 'should': true;
}

type GlobalsCommonjs = {
	readonly 'exports': true;
	readonly 'global': false;
	readonly 'module': false;
	readonly 'require': false;
}

type GlobalsCouch = {
	readonly 'emit': false;
	readonly 'exports': false;
	readonly 'getRow': false;
	readonly 'log': false;
	readonly 'module': false;
	readonly 'provides': false;
	readonly 'require': false;
	readonly 'respond': false;
	readonly 'send': false;
	readonly 'start': false;
	readonly 'sum': false;
}

type GlobalsDevtools = {
	readonly '$': false;
	readonly '$_': false;
	readonly '$$': false;
	readonly '$0': false;
	readonly '$1': false;
	readonly '$2': false;
	readonly '$3': false;
	readonly '$4': false;
	readonly '$x': false;
	readonly 'chrome': false;
	readonly 'clear': false;
	readonly 'copy': false;
	readonly 'debug': false;
	readonly 'dir': false;
	readonly 'dirxml': false;
	readonly 'getEventListeners': false;
	readonly 'inspect': false;
	readonly 'keys': false;
	readonly 'monitor': false;
	readonly 'monitorEvents': false;
	readonly 'profile': false;
	readonly 'profileEnd': false;
	readonly 'queryObjects': false;
	readonly 'table': false;
	readonly 'undebug': false;
	readonly 'unmonitor': false;
	readonly 'unmonitorEvents': false;
	readonly 'values': false;
}

type GlobalsEmbertest = {
	readonly 'andThen': false;
	readonly 'click': false;
	readonly 'currentPath': false;
	readonly 'currentRouteName': false;
	readonly 'currentURL': false;
	readonly 'fillIn': false;
	readonly 'find': false;
	readonly 'findAll': false;
	readonly 'findWithAssert': false;
	readonly 'keyEvent': false;
	readonly 'pauseTest': false;
	readonly 'resumeTest': false;
	readonly 'triggerEvent': false;
	readonly 'visit': false;
	readonly 'wait': false;
}

type GlobalsEs2015 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2016 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2017 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2018 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2019 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2020 = {
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2021 = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2022 = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2023 = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2024 = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsEs2025 = {
	readonly 'AggregateError': false;
	readonly 'Array': false;
	readonly 'ArrayBuffer': false;
	readonly 'Atomics': false;
	readonly 'BigInt': false;
	readonly 'BigInt64Array': false;
	readonly 'BigUint64Array': false;
	readonly 'Boolean': false;
	readonly 'DataView': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'FinalizationRegistry': false;
	readonly 'Float16Array': false;
	readonly 'Float32Array': false;
	readonly 'Float64Array': false;
	readonly 'Function': false;
	readonly 'globalThis': false;
	readonly 'Infinity': false;
	readonly 'Int16Array': false;
	readonly 'Int32Array': false;
	readonly 'Int8Array': false;
	readonly 'Intl': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'Iterator': false;
	readonly 'JSON': false;
	readonly 'Map': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'Promise': false;
	readonly 'Proxy': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'Reflect': false;
	readonly 'RegExp': false;
	readonly 'Set': false;
	readonly 'SharedArrayBuffer': false;
	readonly 'String': false;
	readonly 'Symbol': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'Uint16Array': false;
	readonly 'Uint32Array': false;
	readonly 'Uint8Array': false;
	readonly 'Uint8ClampedArray': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
	readonly 'WeakMap': false;
	readonly 'WeakRef': false;
	readonly 'WeakSet': false;
}

type GlobalsEs3 = {
	readonly 'Array': false;
	readonly 'Boolean': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'RegExp': false;
	readonly 'String': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
}

type GlobalsEs5 = {
	readonly 'Array': false;
	readonly 'Boolean': false;
	readonly 'Date': false;
	readonly 'decodeURI': false;
	readonly 'decodeURIComponent': false;
	readonly 'encodeURI': false;
	readonly 'encodeURIComponent': false;
	readonly 'Error': false;
	readonly 'escape': false;
	readonly 'eval': false;
	readonly 'EvalError': false;
	readonly 'Function': false;
	readonly 'Infinity': false;
	readonly 'isFinite': false;
	readonly 'isNaN': false;
	readonly 'JSON': false;
	readonly 'Math': false;
	readonly 'NaN': false;
	readonly 'Number': false;
	readonly 'Object': false;
	readonly 'parseFloat': false;
	readonly 'parseInt': false;
	readonly 'RangeError': false;
	readonly 'ReferenceError': false;
	readonly 'RegExp': false;
	readonly 'String': false;
	readonly 'SyntaxError': false;
	readonly 'TypeError': false;
	readonly 'undefined': false;
	readonly 'unescape': false;
	readonly 'URIError': false;
}

type GlobalsGreasemonkey = {
	readonly 'cloneInto': false;
	readonly 'createObjectIn': false;
	readonly 'exportFunction': false;
	readonly 'GM': false;
	readonly 'GM_addElement': false;
	readonly 'GM_addStyle': false;
	readonly 'GM_addValueChangeListener': false;
	readonly 'GM_deleteValue': false;
	readonly 'GM_deleteValues': false;
	readonly 'GM_download': false;
	readonly 'GM_getResourceText': false;
	readonly 'GM_getResourceURL': false;
	readonly 'GM_getTab': false;
	readonly 'GM_getTabs': false;
	readonly 'GM_getValue': false;
	readonly 'GM_getValues': false;
	readonly 'GM_info': false;
	readonly 'GM_listValues': false;
	readonly 'GM_log': false;
	readonly 'GM_notification': false;
	readonly 'GM_openInTab': false;
	readonly 'GM_registerMenuCommand': false;
	readonly 'GM_removeValueChangeListener': false;
	readonly 'GM_saveTab': false;
	readonly 'GM_setClipboard': false;
	readonly 'GM_setValue': false;
	readonly 'GM_setValues': false;
	readonly 'GM_unregisterMenuCommand': false;
	readonly 'GM_xmlhttpRequest': false;
	readonly 'unsafeWindow': false;
}

type GlobalsJasmine = {
	readonly 'afterAll': false;
	readonly 'afterEach': false;
	readonly 'beforeAll': false;
	readonly 'beforeEach': false;
	readonly 'describe': false;
	readonly 'expect': false;
	readonly 'expectAsync': false;
	readonly 'fail': false;
	readonly 'fdescribe': false;
	readonly 'fit': false;
	readonly 'it': false;
	readonly 'jasmine': false;
	readonly 'pending': false;
	readonly 'runs': false;
	readonly 'spyOn': false;
	readonly 'spyOnAllFunctions': false;
	readonly 'spyOnProperty': false;
	readonly 'waits': false;
	readonly 'waitsFor': false;
	readonly 'xdescribe': false;
	readonly 'xit': false;
}

type GlobalsJest = {
	readonly 'afterAll': false;
	readonly 'afterEach': false;
	readonly 'beforeAll': false;
	readonly 'beforeEach': false;
	readonly 'describe': false;
	readonly 'expect': false;
	readonly 'fit': false;
	readonly 'it': false;
	readonly 'jest': false;
	readonly 'test': false;
	readonly 'xdescribe': false;
	readonly 'xit': false;
	readonly 'xtest': false;
}

type GlobalsJquery = {
	readonly '$': false;
	readonly 'jQuery': false;
}

type GlobalsMeteor = {
	readonly '$': false;
	readonly 'Accounts': false;
	readonly 'AccountsClient': false;
	readonly 'AccountsCommon': false;
	readonly 'AccountsServer': false;
	readonly 'App': false;
	readonly 'Assets': false;
	readonly 'Blaze': false;
	readonly 'check': false;
	readonly 'Cordova': false;
	readonly 'DDP': false;
	readonly 'DDPRateLimiter': false;
	readonly 'DDPServer': false;
	readonly 'Deps': false;
	readonly 'EJSON': false;
	readonly 'Email': false;
	readonly 'HTTP': false;
	readonly 'Log': false;
	readonly 'Match': false;
	readonly 'Meteor': false;
	readonly 'Mongo': false;
	readonly 'MongoInternals': false;
	readonly 'Npm': false;
	readonly 'Package': false;
	readonly 'Plugin': false;
	readonly 'process': false;
	readonly 'Random': false;
	readonly 'ReactiveDict': false;
	readonly 'ReactiveVar': false;
	readonly 'Router': false;
	readonly 'ServiceConfiguration': false;
	readonly 'Session': false;
	readonly 'share': false;
	readonly 'Spacebars': false;
	readonly 'Template': false;
	readonly 'Tinytest': false;
	readonly 'Tracker': false;
	readonly 'UI': false;
	readonly 'Utils': false;
	readonly 'WebApp': false;
	readonly 'WebAppInternals': false;
}

type GlobalsMocha = {
	readonly 'after': false;
	readonly 'afterEach': false;
	readonly 'before': false;
	readonly 'beforeEach': false;
	readonly 'context': false;
	readonly 'describe': false;
	readonly 'it': false;
	readonly 'mocha': false;
	readonly 'run': false;
	readonly 'setup': false;
	readonly 'specify': false;
	readonly 'suite': false;
	readonly 'suiteSetup': false;
	readonly 'suiteTeardown': false;
	readonly 'teardown': false;
	readonly 'test': false;
	readonly 'xcontext': false;
	readonly 'xdescribe': false;
	readonly 'xit': false;
	readonly 'xspecify': false;
}

type GlobalsMongo = {
	readonly '_isWindows': false;
	readonly '_rand': false;
	readonly 'BulkWriteResult': false;
	readonly 'cat': false;
	readonly 'cd': false;
	readonly 'connect': false;
	readonly 'db': false;
	readonly 'getHostName': false;
	readonly 'getMemInfo': false;
	readonly 'hostname': false;
	readonly 'ISODate': false;
	readonly 'listFiles': false;
	readonly 'load': false;
	readonly 'ls': false;
	readonly 'md5sumFile': false;
	readonly 'mkdir': false;
	readonly 'Mongo': false;
	readonly 'NumberInt': false;
	readonly 'NumberLong': false;
	readonly 'ObjectId': false;
	readonly 'PlanCache': false;
	readonly 'print': false;
	readonly 'printjson': false;
	readonly 'pwd': false;
	readonly 'quit': false;
	readonly 'removeFile': false;
	readonly 'rs': false;
	readonly 'sh': false;
	readonly 'UUID': false;
	readonly 'version': false;
	readonly 'WriteResult': false;
}

type GlobalsNashorn = {
	readonly '__DIR__': false;
	readonly '__FILE__': false;
	readonly '__LINE__': false;
	readonly 'com': false;
	readonly 'edu': false;
	readonly 'exit': false;
	readonly 'java': false;
	readonly 'Java': false;
	readonly 'javafx': false;
	readonly 'JavaImporter': false;
	readonly 'javax': false;
	readonly 'JSAdapter': false;
	readonly 'load': false;
	readonly 'loadWithNewGlobal': false;
	readonly 'org': false;
	readonly 'Packages': false;
	readonly 'print': false;
	readonly 'quit': false;
}

type GlobalsNode = {
	readonly '__dirname': false;
	readonly '__filename': false;
	readonly 'AbortController': false;
	readonly 'AbortSignal': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'Blob': false;
	readonly 'BroadcastChannel': false;
	readonly 'btoa': false;
	readonly 'Buffer': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'clearImmediate': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'CloseEvent': false;
	readonly 'CompressionStream': false;
	readonly 'console': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CustomEvent': false;
	readonly 'DecompressionStream': false;
	readonly 'DisposableStack': false;
	readonly 'DOMException': false;
	readonly 'Event': false;
	readonly 'EventTarget': false;
	readonly 'exports': true;
	readonly 'fetch': false;
	readonly 'File': false;
	readonly 'FormData': false;
	readonly 'global': false;
	readonly 'Headers': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'module': false;
	readonly 'navigator': false;
	readonly 'Navigator': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'process': false;
	readonly 'queueMicrotask': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'Request': false;
	readonly 'require': false;
	readonly 'Response': false;
	readonly 'setImmediate': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'structuredClone': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'WebAssembly': false;
	readonly 'WebSocket': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
}

type GlobalsNodeBuiltin = {
	readonly 'AbortController': false;
	readonly 'AbortSignal': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'Blob': false;
	readonly 'BroadcastChannel': false;
	readonly 'btoa': false;
	readonly 'Buffer': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'clearImmediate': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'CloseEvent': false;
	readonly 'CompressionStream': false;
	readonly 'console': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CustomEvent': false;
	readonly 'DecompressionStream': false;
	readonly 'DisposableStack': false;
	readonly 'DOMException': false;
	readonly 'Event': false;
	readonly 'EventTarget': false;
	readonly 'fetch': false;
	readonly 'File': false;
	readonly 'FormData': false;
	readonly 'global': false;
	readonly 'Headers': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'navigator': false;
	readonly 'Navigator': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'process': false;
	readonly 'queueMicrotask': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'Request': false;
	readonly 'Response': false;
	readonly 'setImmediate': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'structuredClone': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'WebAssembly': false;
	readonly 'WebSocket': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
}

type GlobalsPhantomjs = {
	readonly 'console': true;
	readonly 'exports': true;
	readonly 'phantom': true;
	readonly 'require': true;
	readonly 'WebPage': true;
}

type GlobalsPrototypejs = {
	readonly '$': false;
	readonly '$$': false;
	readonly '$A': false;
	readonly '$break': false;
	readonly '$continue': false;
	readonly '$F': false;
	readonly '$H': false;
	readonly '$R': false;
	readonly '$w': false;
	readonly 'Abstract': false;
	readonly 'Ajax': false;
	readonly 'Autocompleter': false;
	readonly 'Builder': false;
	readonly 'Class': false;
	readonly 'Control': false;
	readonly 'Draggable': false;
	readonly 'Draggables': false;
	readonly 'Droppables': false;
	readonly 'Effect': false;
	readonly 'Element': false;
	readonly 'Enumerable': false;
	readonly 'Event': false;
	readonly 'Field': false;
	readonly 'Form': false;
	readonly 'Hash': false;
	readonly 'Insertion': false;
	readonly 'ObjectRange': false;
	readonly 'PeriodicalExecuter': false;
	readonly 'Position': false;
	readonly 'Prototype': false;
	readonly 'Scriptaculous': false;
	readonly 'Selector': false;
	readonly 'Sortable': false;
	readonly 'SortableObserver': false;
	readonly 'Sound': false;
	readonly 'Template': false;
	readonly 'Toggle': false;
	readonly 'Try': false;
}

type GlobalsProtractor = {
	readonly '$': false;
	readonly '$$': false;
	readonly 'browser': false;
	readonly 'by': false;
	readonly 'By': false;
	readonly 'DartObject': false;
	readonly 'element': false;
	readonly 'protractor': false;
}

type GlobalsQunit = {
	readonly 'asyncTest': false;
	readonly 'deepEqual': false;
	readonly 'equal': false;
	readonly 'expect': false;
	readonly 'module': false;
	readonly 'notDeepEqual': false;
	readonly 'notEqual': false;
	readonly 'notOk': false;
	readonly 'notPropEqual': false;
	readonly 'notStrictEqual': false;
	readonly 'ok': false;
	readonly 'propEqual': false;
	readonly 'QUnit': false;
	readonly 'raises': false;
	readonly 'start': false;
	readonly 'stop': false;
	readonly 'strictEqual': false;
	readonly 'test': false;
	readonly 'throws': false;
}

type GlobalsRhino = {
	readonly 'defineClass': false;
	readonly 'deserialize': false;
	readonly 'gc': false;
	readonly 'help': false;
	readonly 'importClass': false;
	readonly 'importPackage': false;
	readonly 'java': false;
	readonly 'load': false;
	readonly 'loadClass': false;
	readonly 'Packages': false;
	readonly 'print': false;
	readonly 'quit': false;
	readonly 'readFile': false;
	readonly 'readUrl': false;
	readonly 'runCommand': false;
	readonly 'seal': false;
	readonly 'serialize': false;
	readonly 'spawn': false;
	readonly 'sync': false;
	readonly 'toint32': false;
	readonly 'version': false;
}

type GlobalsServiceworker = {
	readonly 'AbortController': false;
	readonly 'AbortPaymentEvent': false;
	readonly 'AbortSignal': false;
	readonly 'addEventListener': false;
	readonly 'ai': false;
	readonly 'AI': false;
	readonly 'AICreateMonitor': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'BackgroundFetchEvent': false;
	readonly 'BackgroundFetchManager': false;
	readonly 'BackgroundFetchRecord': false;
	readonly 'BackgroundFetchRegistration': false;
	readonly 'BackgroundFetchUpdateUIEvent': false;
	readonly 'BarcodeDetector': false;
	readonly 'Blob': false;
	readonly 'BroadcastChannel': false;
	readonly 'btoa': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'Cache': false;
	readonly 'caches': false;
	readonly 'CacheStorage': false;
	readonly 'CanMakePaymentEvent': false;
	readonly 'CanvasGradient': false;
	readonly 'CanvasPattern': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'Client': false;
	readonly 'clients': false;
	readonly 'Clients': false;
	readonly 'CloseEvent': false;
	readonly 'CompressionStream': false;
	readonly 'console': false;
	readonly 'cookieStore': false;
	readonly 'CookieStore': false;
	readonly 'CookieStoreManager': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'createImageBitmap': false;
	readonly 'CropTarget': false;
	readonly 'crossOriginIsolated': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CSSSkewX': false;
	readonly 'CSSSkewY': false;
	readonly 'CustomEvent': false;
	readonly 'DecompressionStream': false;
	readonly 'dispatchEvent': false;
	readonly 'DisposableStack': false;
	readonly 'DOMException': false;
	readonly 'DOMMatrix': false;
	readonly 'DOMMatrixReadOnly': false;
	readonly 'DOMPoint': false;
	readonly 'DOMPointReadOnly': false;
	readonly 'DOMQuad': false;
	readonly 'DOMRect': false;
	readonly 'DOMRectReadOnly': false;
	readonly 'DOMStringList': false;
	readonly 'ErrorEvent': false;
	readonly 'Event': false;
	readonly 'EventSource': false;
	readonly 'EventTarget': false;
	readonly 'ExtendableCookieChangeEvent': false;
	readonly 'ExtendableEvent': false;
	readonly 'ExtendableMessageEvent': false;
	readonly 'fetch': false;
	readonly 'FetchEvent': false;
	readonly 'File': false;
	readonly 'FileList': false;
	readonly 'FileReader': false;
	readonly 'FileSystemDirectoryHandle': false;
	readonly 'FileSystemFileHandle': false;
	readonly 'FileSystemHandle': false;
	readonly 'FileSystemWritableFileStream': false;
	readonly 'FontFace': false;
	readonly 'fonts': false;
	readonly 'FormData': false;
	readonly 'GPU': false;
	readonly 'GPUAdapter': false;
	readonly 'GPUAdapterInfo': false;
	readonly 'GPUBindGroup': false;
	readonly 'GPUBindGroupLayout': false;
	readonly 'GPUBuffer': false;
	readonly 'GPUBufferUsage': false;
	readonly 'GPUCanvasContext': false;
	readonly 'GPUColorWrite': false;
	readonly 'GPUCommandBuffer': false;
	readonly 'GPUCommandEncoder': false;
	readonly 'GPUCompilationInfo': false;
	readonly 'GPUCompilationMessage': false;
	readonly 'GPUComputePassEncoder': false;
	readonly 'GPUComputePipeline': false;
	readonly 'GPUDevice': false;
	readonly 'GPUDeviceLostInfo': false;
	readonly 'GPUError': false;
	readonly 'GPUExternalTexture': false;
	readonly 'GPUInternalError': false;
	readonly 'GPUMapMode': false;
	readonly 'GPUOutOfMemoryError': false;
	readonly 'GPUPipelineError': false;
	readonly 'GPUPipelineLayout': false;
	readonly 'GPUQuerySet': false;
	readonly 'GPUQueue': false;
	readonly 'GPURenderBundle': false;
	readonly 'GPURenderBundleEncoder': false;
	readonly 'GPURenderPassEncoder': false;
	readonly 'GPURenderPipeline': false;
	readonly 'GPUSampler': false;
	readonly 'GPUShaderModule': false;
	readonly 'GPUShaderStage': false;
	readonly 'GPUSupportedFeatures': false;
	readonly 'GPUSupportedLimits': false;
	readonly 'GPUTexture': false;
	readonly 'GPUTextureUsage': false;
	readonly 'GPUTextureView': false;
	readonly 'GPUUncapturedErrorEvent': false;
	readonly 'GPUValidationError': false;
	readonly 'Headers': false;
	readonly 'IDBCursor': false;
	readonly 'IDBCursorWithValue': false;
	readonly 'IDBDatabase': false;
	readonly 'IDBFactory': false;
	readonly 'IDBIndex': false;
	readonly 'IDBKeyRange': false;
	readonly 'IDBObjectStore': false;
	readonly 'IDBOpenDBRequest': false;
	readonly 'IDBRequest': false;
	readonly 'IDBTransaction': false;
	readonly 'IDBVersionChangeEvent': false;
	readonly 'ImageBitmap': false;
	readonly 'ImageBitmapRenderingContext': false;
	readonly 'ImageData': false;
	readonly 'importScripts': false;
	readonly 'indexedDB': false;
	readonly 'InstallEvent': false;
	readonly 'isSecureContext': false;
	readonly 'LanguageDetector': false;
	readonly 'location': false;
	readonly 'Lock': false;
	readonly 'LockManager': false;
	readonly 'MediaCapabilities': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'NavigationPreloadManager': false;
	readonly 'navigator': false;
	readonly 'NavigatorUAData': false;
	readonly 'NetworkInformation': false;
	readonly 'Notification': false;
	readonly 'NotificationEvent': false;
	readonly 'Observable': false;
	readonly 'OffscreenCanvas': false;
	readonly 'OffscreenCanvasRenderingContext2D': false;
	readonly 'onabortpayment': true;
	readonly 'onactivate': true;
	readonly 'onbackgroundfetchabort': true;
	readonly 'onbackgroundfetchclick': true;
	readonly 'onbackgroundfetchfail': true;
	readonly 'onbackgroundfetchsuccess': true;
	readonly 'oncanmakepayment': true;
	readonly 'oncookiechange': true;
	readonly 'onerror': true;
	readonly 'onfetch': true;
	readonly 'oninstall': true;
	readonly 'onlanguagechange': true;
	readonly 'onmessage': true;
	readonly 'onmessageerror': true;
	readonly 'onnotificationclick': true;
	readonly 'onnotificationclose': true;
	readonly 'onpaymentrequest': true;
	readonly 'onperiodicsync': true;
	readonly 'onpush': true;
	readonly 'onpushsubscriptionchange': true;
	readonly 'onrejectionhandled': true;
	readonly 'onsync': true;
	readonly 'onunhandledrejection': true;
	readonly 'origin': false;
	readonly 'Path2D': false;
	readonly 'PaymentRequestEvent': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'PerformanceServerTiming': false;
	readonly 'PeriodicSyncEvent': false;
	readonly 'PeriodicSyncManager': false;
	readonly 'Permissions': false;
	readonly 'PermissionStatus': false;
	readonly 'PromiseRejectionEvent': false;
	readonly 'PushEvent': false;
	readonly 'PushManager': false;
	readonly 'PushMessageData': false;
	readonly 'PushSubscription': false;
	readonly 'PushSubscriptionOptions': false;
	readonly 'queueMicrotask': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'registration': false;
	readonly 'removeEventListener': false;
	readonly 'ReportBody': false;
	readonly 'reportError': false;
	readonly 'ReportingObserver': false;
	readonly 'Request': false;
	readonly 'Response': false;
	readonly 'RestrictionTarget': false;
	readonly 'scheduler': false;
	readonly 'Scheduler': false;
	readonly 'SecurityPolicyViolationEvent': false;
	readonly 'self': false;
	readonly 'serviceWorker': false;
	readonly 'ServiceWorker': false;
	readonly 'ServiceWorkerGlobalScope': false;
	readonly 'ServiceWorkerRegistration': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'skipWaiting': false;
	readonly 'StorageBucket': false;
	readonly 'StorageBucketManager': false;
	readonly 'StorageManager': false;
	readonly 'structuredClone': false;
	readonly 'Subscriber': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'SyncEvent': false;
	readonly 'SyncManager': false;
	readonly 'TaskController': false;
	readonly 'TaskPriorityChangeEvent': false;
	readonly 'TaskSignal': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TextMetrics': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'TrustedHTML': false;
	readonly 'TrustedScript': false;
	readonly 'TrustedScriptURL': false;
	readonly 'TrustedTypePolicy': false;
	readonly 'TrustedTypePolicyFactory': false;
	readonly 'trustedTypes': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'UserActivation': false;
	readonly 'WebAssembly': false;
	readonly 'WebGL2RenderingContext': false;
	readonly 'WebGLActiveInfo': false;
	readonly 'WebGLBuffer': false;
	readonly 'WebGLContextEvent': false;
	readonly 'WebGLFramebuffer': false;
	readonly 'WebGLObject': false;
	readonly 'WebGLProgram': false;
	readonly 'WebGLQuery': false;
	readonly 'WebGLRenderbuffer': false;
	readonly 'WebGLRenderingContext': false;
	readonly 'WebGLSampler': false;
	readonly 'WebGLShader': false;
	readonly 'WebGLShaderPrecisionFormat': false;
	readonly 'WebGLSync': false;
	readonly 'WebGLTexture': false;
	readonly 'WebGLTransformFeedback': false;
	readonly 'WebGLUniformLocation': false;
	readonly 'WebGLVertexArrayObject': false;
	readonly 'WebSocket': false;
	readonly 'WebSocketError': false;
	readonly 'WebSocketStream': false;
	readonly 'WebTransport': false;
	readonly 'WebTransportBidirectionalStream': false;
	readonly 'WebTransportDatagramDuplexStream': false;
	readonly 'WebTransportError': false;
	readonly 'WGSLLanguageFeatures': false;
	readonly 'when': false;
	readonly 'WindowClient': false;
	readonly 'WorkerGlobalScope': false;
	readonly 'WorkerLocation': false;
	readonly 'WorkerNavigator': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
}

type GlobalsSharednodebrowser = {
	readonly 'AbortController': false;
	readonly 'AbortSignal': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'Blob': false;
	readonly 'BroadcastChannel': false;
	readonly 'btoa': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'CloseEvent': false;
	readonly 'CompressionStream': false;
	readonly 'console': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CustomEvent': false;
	readonly 'DecompressionStream': false;
	readonly 'DisposableStack': false;
	readonly 'DOMException': false;
	readonly 'Event': false;
	readonly 'EventTarget': false;
	readonly 'fetch': false;
	readonly 'File': false;
	readonly 'FormData': false;
	readonly 'Headers': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'navigator': false;
	readonly 'Navigator': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'queueMicrotask': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'Request': false;
	readonly 'Response': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'structuredClone': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'WebAssembly': false;
	readonly 'WebSocket': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
}

type GlobalsShelljs = {
	readonly 'cat': false;
	readonly 'cd': false;
	readonly 'chmod': false;
	readonly 'cmd': false;
	readonly 'config': false;
	readonly 'cp': false;
	readonly 'dirs': false;
	readonly 'echo': false;
	readonly 'env': false;
	readonly 'error': false;
	readonly 'errorCode': false;
	readonly 'exec': false;
	readonly 'exit': false;
	readonly 'find': false;
	readonly 'grep': false;
	readonly 'head': false;
	readonly 'ln': false;
	readonly 'ls': false;
	readonly 'mkdir': false;
	readonly 'mv': false;
	readonly 'popd': false;
	readonly 'pushd': false;
	readonly 'pwd': false;
	readonly 'rm': false;
	readonly 'sed': false;
	readonly 'set': false;
	readonly 'ShellString': false;
	readonly 'sort': false;
	readonly 'tail': false;
	readonly 'tempdir': false;
	readonly 'test': false;
	readonly 'touch': false;
	readonly 'uniq': false;
	readonly 'which': false;
}

type GlobalsVitest = {
	readonly 'afterAll': false;
	readonly 'afterEach': false;
	readonly 'assert': false;
	readonly 'assertType': false;
	readonly 'beforeAll': false;
	readonly 'beforeEach': false;
	readonly 'chai': false;
	readonly 'describe': false;
	readonly 'expect': false;
	readonly 'expectTypeOf': false;
	readonly 'it': false;
	readonly 'onTestFailed': false;
	readonly 'onTestFinished': false;
	readonly 'suite': false;
	readonly 'test': false;
	readonly 'vi': false;
	readonly 'vitest': false;
}

type GlobalsWebextensions = {
	readonly 'browser': false;
	readonly 'chrome': false;
	readonly 'opr': false;
}

type GlobalsWorker = {
	readonly 'AbortController': false;
	readonly 'AbortSignal': false;
	readonly 'addEventListener': false;
	readonly 'ai': false;
	readonly 'AI': false;
	readonly 'AICreateMonitor': false;
	readonly 'AsyncDisposableStack': false;
	readonly 'atob': false;
	readonly 'AudioData': false;
	readonly 'AudioDecoder': false;
	readonly 'AudioEncoder': false;
	readonly 'BackgroundFetchManager': false;
	readonly 'BackgroundFetchRecord': false;
	readonly 'BackgroundFetchRegistration': false;
	readonly 'BarcodeDetector': false;
	readonly 'Blob': false;
	readonly 'BroadcastChannel': false;
	readonly 'btoa': false;
	readonly 'ByteLengthQueuingStrategy': false;
	readonly 'Cache': false;
	readonly 'caches': false;
	readonly 'CacheStorage': false;
	readonly 'cancelAnimationFrame': false;
	readonly 'CanvasGradient': false;
	readonly 'CanvasPattern': false;
	readonly 'clearInterval': false;
	readonly 'clearTimeout': false;
	readonly 'close': false;
	readonly 'CloseEvent': false;
	readonly 'CompressionStream': false;
	readonly 'console': false;
	readonly 'CountQueuingStrategy': false;
	readonly 'createImageBitmap': false;
	readonly 'CropTarget': false;
	readonly 'crossOriginIsolated': false;
	readonly 'crypto': false;
	readonly 'Crypto': false;
	readonly 'CryptoKey': false;
	readonly 'CSSSkewX': false;
	readonly 'CSSSkewY': false;
	readonly 'CustomEvent': false;
	readonly 'DecompressionStream': false;
	readonly 'DedicatedWorkerGlobalScope': false;
	readonly 'dispatchEvent': false;
	readonly 'DisposableStack': false;
	readonly 'DOMException': false;
	readonly 'DOMMatrix': false;
	readonly 'DOMMatrixReadOnly': false;
	readonly 'DOMPoint': false;
	readonly 'DOMPointReadOnly': false;
	readonly 'DOMQuad': false;
	readonly 'DOMRect': false;
	readonly 'DOMRectReadOnly': false;
	readonly 'DOMStringList': false;
	readonly 'EncodedAudioChunk': false;
	readonly 'EncodedVideoChunk': false;
	readonly 'ErrorEvent': false;
	readonly 'Event': false;
	readonly 'EventSource': false;
	readonly 'EventTarget': false;
	readonly 'fetch': false;
	readonly 'File': false;
	readonly 'FileList': false;
	readonly 'FileReader': false;
	readonly 'FileReaderSync': false;
	readonly 'FileSystemDirectoryHandle': false;
	readonly 'FileSystemFileHandle': false;
	readonly 'FileSystemHandle': false;
	readonly 'FileSystemObserver': false;
	readonly 'FileSystemSyncAccessHandle': false;
	readonly 'FileSystemWritableFileStream': false;
	readonly 'FontFace': false;
	readonly 'fonts': false;
	readonly 'FormData': false;
	readonly 'GPU': false;
	readonly 'GPUAdapter': false;
	readonly 'GPUAdapterInfo': false;
	readonly 'GPUBindGroup': false;
	readonly 'GPUBindGroupLayout': false;
	readonly 'GPUBuffer': false;
	readonly 'GPUBufferUsage': false;
	readonly 'GPUCanvasContext': false;
	readonly 'GPUColorWrite': false;
	readonly 'GPUCommandBuffer': false;
	readonly 'GPUCommandEncoder': false;
	readonly 'GPUCompilationInfo': false;
	readonly 'GPUCompilationMessage': false;
	readonly 'GPUComputePassEncoder': false;
	readonly 'GPUComputePipeline': false;
	readonly 'GPUDevice': false;
	readonly 'GPUDeviceLostInfo': false;
	readonly 'GPUError': false;
	readonly 'GPUExternalTexture': false;
	readonly 'GPUInternalError': false;
	readonly 'GPUMapMode': false;
	readonly 'GPUOutOfMemoryError': false;
	readonly 'GPUPipelineError': false;
	readonly 'GPUPipelineLayout': false;
	readonly 'GPUQuerySet': false;
	readonly 'GPUQueue': false;
	readonly 'GPURenderBundle': false;
	readonly 'GPURenderBundleEncoder': false;
	readonly 'GPURenderPassEncoder': false;
	readonly 'GPURenderPipeline': false;
	readonly 'GPUSampler': false;
	readonly 'GPUShaderModule': false;
	readonly 'GPUShaderStage': false;
	readonly 'GPUSupportedFeatures': false;
	readonly 'GPUSupportedLimits': false;
	readonly 'GPUTexture': false;
	readonly 'GPUTextureUsage': false;
	readonly 'GPUTextureView': false;
	readonly 'GPUUncapturedErrorEvent': false;
	readonly 'GPUValidationError': false;
	readonly 'Headers': false;
	readonly 'HID': false;
	readonly 'HIDConnectionEvent': false;
	readonly 'HIDDevice': false;
	readonly 'HIDInputReportEvent': false;
	readonly 'IDBCursor': false;
	readonly 'IDBCursorWithValue': false;
	readonly 'IDBDatabase': false;
	readonly 'IDBFactory': false;
	readonly 'IDBIndex': false;
	readonly 'IDBKeyRange': false;
	readonly 'IDBObjectStore': false;
	readonly 'IDBOpenDBRequest': false;
	readonly 'IDBRequest': false;
	readonly 'IDBTransaction': false;
	readonly 'IDBVersionChangeEvent': false;
	readonly 'IdleDetector': false;
	readonly 'ImageBitmap': false;
	readonly 'ImageBitmapRenderingContext': false;
	readonly 'ImageData': false;
	readonly 'ImageDecoder': false;
	readonly 'ImageTrack': false;
	readonly 'ImageTrackList': false;
	readonly 'importScripts': false;
	readonly 'indexedDB': false;
	readonly 'isSecureContext': false;
	readonly 'LanguageDetector': false;
	readonly 'location': false;
	readonly 'Lock': false;
	readonly 'LockManager': false;
	readonly 'MediaCapabilities': false;
	readonly 'MediaSource': false;
	readonly 'MediaSourceHandle': false;
	readonly 'MessageChannel': false;
	readonly 'MessageEvent': false;
	readonly 'MessagePort': false;
	readonly 'name': false;
	readonly 'NavigationPreloadManager': false;
	readonly 'navigator': false;
	readonly 'NavigatorUAData': false;
	readonly 'NetworkInformation': false;
	readonly 'Notification': false;
	readonly 'Observable': false;
	readonly 'OffscreenCanvas': false;
	readonly 'OffscreenCanvasRenderingContext2D': false;
	readonly 'onerror': true;
	readonly 'onlanguagechange': true;
	readonly 'onmessage': true;
	readonly 'onmessageerror': true;
	readonly 'onrejectionhandled': true;
	readonly 'onunhandledrejection': true;
	readonly 'origin': false;
	readonly 'Path2D': false;
	readonly 'performance': false;
	readonly 'Performance': false;
	readonly 'PerformanceEntry': false;
	readonly 'PerformanceMark': false;
	readonly 'PerformanceMeasure': false;
	readonly 'PerformanceObserver': false;
	readonly 'PerformanceObserverEntryList': false;
	readonly 'PerformanceResourceTiming': false;
	readonly 'PerformanceServerTiming': false;
	readonly 'PeriodicSyncManager': false;
	readonly 'Permissions': false;
	readonly 'PermissionStatus': false;
	readonly 'PERSISTENT': false;
	readonly 'postMessage': false;
	readonly 'PressureObserver': false;
	readonly 'PressureRecord': false;
	readonly 'ProgressEvent': false;
	readonly 'PromiseRejectionEvent': false;
	readonly 'PushManager': false;
	readonly 'PushSubscription': false;
	readonly 'PushSubscriptionOptions': false;
	readonly 'queueMicrotask': false;
	readonly 'ReadableByteStreamController': false;
	readonly 'ReadableStream': false;
	readonly 'ReadableStreamBYOBReader': false;
	readonly 'ReadableStreamBYOBRequest': false;
	readonly 'ReadableStreamDefaultController': false;
	readonly 'ReadableStreamDefaultReader': false;
	readonly 'removeEventListener': false;
	readonly 'ReportBody': false;
	readonly 'reportError': false;
	readonly 'ReportingObserver': false;
	readonly 'Request': false;
	readonly 'requestAnimationFrame': false;
	readonly 'Response': false;
	readonly 'RestrictionTarget': false;
	readonly 'RTCDataChannel': false;
	readonly 'RTCEncodedAudioFrame': false;
	readonly 'RTCEncodedVideoFrame': false;
	readonly 'scheduler': false;
	readonly 'Scheduler': false;
	readonly 'SecurityPolicyViolationEvent': false;
	readonly 'self': false;
	readonly 'Serial': false;
	readonly 'SerialPort': false;
	readonly 'ServiceWorkerRegistration': false;
	readonly 'setInterval': false;
	readonly 'setTimeout': false;
	readonly 'SourceBuffer': false;
	readonly 'SourceBufferList': false;
	readonly 'StorageBucket': false;
	readonly 'StorageBucketManager': false;
	readonly 'StorageManager': false;
	readonly 'structuredClone': false;
	readonly 'Subscriber': false;
	readonly 'SubtleCrypto': false;
	readonly 'SuppressedError': false;
	readonly 'SyncManager': false;
	readonly 'TaskController': false;
	readonly 'TaskPriorityChangeEvent': false;
	readonly 'TaskSignal': false;
	readonly 'TEMPORARY': false;
	readonly 'TextDecoder': false;
	readonly 'TextDecoderStream': false;
	readonly 'TextEncoder': false;
	readonly 'TextEncoderStream': false;
	readonly 'TextMetrics': false;
	readonly 'TransformStream': false;
	readonly 'TransformStreamDefaultController': false;
	readonly 'TrustedHTML': false;
	readonly 'TrustedScript': false;
	readonly 'TrustedScriptURL': false;
	readonly 'TrustedTypePolicy': false;
	readonly 'TrustedTypePolicyFactory': false;
	readonly 'trustedTypes': false;
	readonly 'URL': false;
	readonly 'URLPattern': false;
	readonly 'URLSearchParams': false;
	readonly 'USB': false;
	readonly 'USBAlternateInterface': false;
	readonly 'USBConfiguration': false;
	readonly 'USBConnectionEvent': false;
	readonly 'USBDevice': false;
	readonly 'USBEndpoint': false;
	readonly 'USBInterface': false;
	readonly 'USBInTransferResult': false;
	readonly 'USBIsochronousInTransferPacket': false;
	readonly 'USBIsochronousInTransferResult': false;
	readonly 'USBIsochronousOutTransferPacket': false;
	readonly 'USBIsochronousOutTransferResult': false;
	readonly 'USBOutTransferResult': false;
	readonly 'UserActivation': false;
	readonly 'VideoColorSpace': false;
	readonly 'VideoDecoder': false;
	readonly 'VideoEncoder': false;
	readonly 'VideoFrame': false;
	readonly 'WebAssembly': false;
	readonly 'WebGL2RenderingContext': false;
	readonly 'WebGLActiveInfo': false;
	readonly 'WebGLBuffer': false;
	readonly 'WebGLContextEvent': false;
	readonly 'WebGLFramebuffer': false;
	readonly 'WebGLObject': false;
	readonly 'WebGLProgram': false;
	readonly 'WebGLQuery': false;
	readonly 'WebGLRenderbuffer': false;
	readonly 'WebGLRenderingContext': false;
	readonly 'WebGLSampler': false;
	readonly 'WebGLShader': false;
	readonly 'WebGLShaderPrecisionFormat': false;
	readonly 'WebGLSync': false;
	readonly 'WebGLTexture': false;
	readonly 'WebGLTransformFeedback': false;
	readonly 'WebGLUniformLocation': false;
	readonly 'WebGLVertexArrayObject': false;
	readonly 'webkitRequestFileSystem': false;
	readonly 'webkitRequestFileSystemSync': false;
	readonly 'webkitResolveLocalFileSystemSyncURL': false;
	readonly 'webkitResolveLocalFileSystemURL': false;
	readonly 'WebSocket': false;
	readonly 'WebSocketError': false;
	readonly 'WebSocketStream': false;
	readonly 'WebTransport': false;
	readonly 'WebTransportBidirectionalStream': false;
	readonly 'WebTransportDatagramDuplexStream': false;
	readonly 'WebTransportError': false;
	readonly 'WGSLLanguageFeatures': false;
	readonly 'when': false;
	readonly 'Worker': false;
	readonly 'WorkerGlobalScope': false;
	readonly 'WorkerLocation': false;
	readonly 'WorkerNavigator': false;
	readonly 'WritableStream': false;
	readonly 'WritableStreamDefaultController': false;
	readonly 'WritableStreamDefaultWriter': false;
	readonly 'XMLHttpRequest': false;
	readonly 'XMLHttpRequestEventTarget': false;
	readonly 'XMLHttpRequestUpload': false;
}

type GlobalsWsh = {
	readonly 'ActiveXObject': false;
	readonly 'CollectGarbage': false;
	readonly 'Debug': false;
	readonly 'Enumerator': false;
	readonly 'GetObject': false;
	readonly 'RuntimeObject': false;
	readonly 'ScriptEngine': false;
	readonly 'ScriptEngineBuildVersion': false;
	readonly 'ScriptEngineMajorVersion': false;
	readonly 'ScriptEngineMinorVersion': false;
	readonly 'VBArray': false;
	readonly 'WScript': false;
	readonly 'WSH': false;
}

type GlobalsYui = {
	readonly 'YAHOO': false;
	readonly 'YAHOO_config': false;
	readonly 'YUI': false;
	readonly 'YUI_config': false;
}

type Globals = {
	readonly 'amd': GlobalsAmd;
	readonly 'applescript': GlobalsApplescript;
	readonly 'atomtest': GlobalsAtomtest;
	readonly 'browser': GlobalsBrowser;
	readonly 'builtin': GlobalsBuiltin;
	readonly 'chai': GlobalsChai;
	readonly 'commonjs': GlobalsCommonjs;
	readonly 'couch': GlobalsCouch;
	readonly 'devtools': GlobalsDevtools;
	readonly 'embertest': GlobalsEmbertest;
	readonly 'es2015': GlobalsEs2015;
	readonly 'es2016': GlobalsEs2016;
	readonly 'es2017': GlobalsEs2017;
	readonly 'es2018': GlobalsEs2018;
	readonly 'es2019': GlobalsEs2019;
	readonly 'es2020': GlobalsEs2020;
	readonly 'es2021': GlobalsEs2021;
	readonly 'es2022': GlobalsEs2022;
	readonly 'es2023': GlobalsEs2023;
	readonly 'es2024': GlobalsEs2024;
	readonly 'es2025': GlobalsEs2025;
	readonly 'es3': GlobalsEs3;
	readonly 'es5': GlobalsEs5;
	readonly 'greasemonkey': GlobalsGreasemonkey;
	readonly 'jasmine': GlobalsJasmine;
	readonly 'jest': GlobalsJest;
	readonly 'jquery': GlobalsJquery;
	readonly 'meteor': GlobalsMeteor;
	readonly 'mocha': GlobalsMocha;
	readonly 'mongo': GlobalsMongo;
	readonly 'nashorn': GlobalsNashorn;
	readonly 'node': GlobalsNode;
	readonly 'nodeBuiltin': GlobalsNodeBuiltin;
	readonly 'phantomjs': GlobalsPhantomjs;
	readonly 'prototypejs': GlobalsPrototypejs;
	readonly 'protractor': GlobalsProtractor;
	readonly 'qunit': GlobalsQunit;
	readonly 'rhino': GlobalsRhino;
	readonly 'serviceworker': GlobalsServiceworker;
	readonly 'shared-node-browser': GlobalsSharednodebrowser;
	readonly 'shelljs': GlobalsShelljs;
	readonly 'vitest': GlobalsVitest;
	readonly 'webextensions': GlobalsWebextensions;
	readonly 'worker': GlobalsWorker;
	readonly 'wsh': GlobalsWsh;
	readonly 'yui': GlobalsYui;
}

declare const globals: Globals;

export = globals;